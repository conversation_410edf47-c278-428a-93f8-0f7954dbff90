import { createSelector } from "@reduxjs/toolkit";
import { RootState } from "./index";

// Base selectors
export const selectSearch = (state: RootState) => state.search;
export const selectCategory = (state: RootState) => state.category;
export const selectFilter = (state: RootState) => state.filter;
export const selectProduct = (state: RootState) => state.product;
export const selectCart = (state: RootState) => state.cart;
export const selectUser = (state: RootState) => state.user;
export const selectModal = (state: RootState) => state.modal;

// Search selectors
export const selectSearchQuery = createSelector(
  [selectSearch],
  (search) => search.query
);

export const selectSearchLocation = createSelector(
  [selectSearch],
  (search) => search.location
);

export const selectSearchResults = createSelector(
  [selectSearch],
  (search) => search.results
);

export const selectSearchLoading = createSelector(
  [selectSearch],
  (search) => search.loading
);

// Category selectors
export const selectSelectedCategory = createSelector(
  [selectCategory],
  (category) => category.selectedCategory
);

export const selectCategories = createSelector(
  [selectCategory],
  (category) => category.categories
);

export const selectShowFilters = createSelector(
  [selectCategory],
  (category) => category.showFilters
);

// Filter selectors
export const selectActiveFilters = createSelector(
  [selectFilter],
  (filter) => filter.activeFilters
);

export const selectAvailableFilters = createSelector(
  [selectFilter],
  (filter) => filter.availableFilters
);

export const selectActiveFiltersCount = createSelector(
  [selectActiveFilters],
  (activeFilters) => Object.keys(activeFilters).length
);

// Product UI state selectors (simplified for API-driven approach)
export const selectCurrentPage = createSelector(
  [selectProduct],
  (product) => product.currentPage
);

export const selectSortBy = createSelector(
  [selectProduct],
  (product) => product.sortBy
);

export const selectItemsPerPage = createSelector(
  [selectProduct],
  (product) => product.itemsPerPage
);

// Compatibility selectors for components that expect products data
// These will return empty arrays/false since we're using RTK Query for actual data
export const selectProducts = createSelector(
  [selectProduct],
  (product) => [] // Empty array since products are fetched via RTK Query
);

export const selectFilteredProducts = createSelector(
  [selectProduct],
  (product) => [] // Empty array since filtering is handled by RTK Query params
);

export const selectProductsLoading = createSelector(
  [selectProduct],
  (product) => false // RTK Query handles loading states via hooks
);

// Cart selectors
export const selectCartItems = createSelector(
  [selectCart],
  (cart) => cart.items
);

export const selectCartTotalItems = createSelector(
  [selectCart],
  (cart) => cart.totalItems
);

export const selectCartTotalPrice = createSelector(
  [selectCart],
  (cart) => cart.totalPrice
);

export const selectCartCurrency = createSelector(
  [selectCart],
  (cart) => cart.currency
);

export const selectCartLoading = createSelector(
  [selectCart],
  (cart) => cart.loading
);

export const selectIsCartEmpty = createSelector(
  [selectCartItems],
  (items) => items.length === 0
);

// Cart item by product ID selector
export const selectCartItemByProductId = createSelector(
  [selectCartItems, (state: RootState, productId: string) => productId],
  (items, productId) => items.find((item: any) => item.id === productId)
);

// User selectors
export const selectCurrentUser = createSelector(
  [selectUser],
  (user) => user.currentUser
);

export const selectIsAuthenticated = createSelector(
  [selectUser],
  (user) => user.isAuthenticated
);

export const selectUserLoading = createSelector(
  [selectUser],
  (user) => user.isLoading
);

export const selectUserError = createSelector(
  [selectUser],
  (user) => user.error
);

export const selectUserSavedListings = createSelector(
  [selectCurrentUser],
  (user) => user?.savedListings || []
);

// Check if product is saved by user
export const selectIsProductSaved = createSelector(
  [selectUserSavedListings, (state: RootState, productId: string) => productId],
  (savedListings, productId) => savedListings.includes(productId)
);

// Modal selectors
export const selectModalIsOpen = createSelector(
  [selectModal],
  (modal) => modal.isOpen
);

export const selectModalType = createSelector(
  [selectModal],
  (modal) => modal.modalType
);

export const selectModalProps = createSelector(
  [selectModal],
  (modal) => modal.modalProps
);

export const selectModalData = createSelector(
  [selectModal],
  (modal) => modal.modalData
);

// Complex selectors combining multiple states
export const selectEcommerceState = createSelector(
  [selectSearch, selectCategory, selectFilter, selectProduct, selectCart],
  (search, category, filter, product, cart) => ({
    search,
    category,
    filter,
    product,
    cart,
  })
);

// Loading states selector
export const selectGlobalLoading = createSelector(
  [selectSearchLoading, selectCartLoading, selectUserLoading],
  (searchLoading, cartLoading, userLoading) =>
    searchLoading || cartLoading || userLoading
);

// Error states selector
export const selectGlobalError = createSelector(
  [selectSearch, selectProduct, selectCart, selectUser],
  (search, product, cart, user) =>
    search.error || product.error || cart.error || user.error
);
