import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface NavigationState {
  pendingNavigation: string | null;
}

const initialState: NavigationState = {
  pendingNavigation: null,
};

const navigationSlice = createSlice({
  name: "navigation",
  initialState,
  reducers: {
    navigateTo: (state, action: PayloadAction<string>) => {
      state.pendingNavigation = action.payload;
    },
    clearPendingNavigation: (state) => {
      state.pendingNavigation = null;
    },
  },
});

export const { navigateTo, clearPendingNavigation } = navigationSlice.actions;
export default navigationSlice.reducer;
