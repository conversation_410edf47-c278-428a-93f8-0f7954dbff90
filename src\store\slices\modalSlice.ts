import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Define the modal state interface - now serializable
export interface ModalState {
  isOpen: boolean;
  modalType: string | null;
  modalProps: Record<string, unknown>;
  modalData?: any; // Serializable data to pass to the modal component
}

// Initial state
const initialState: ModalState = {
  isOpen: false,
  modalType: null,
  modalProps: {},
  modalData: undefined,
};

// Create the modal slice
const modalSlice = createSlice({
  name: "modal",
  initialState,
  reducers: {
    openModal: (
      state,
      action: PayloadAction<{
        type: string;
        props?: Record<string, unknown>;
        data?: any;
      }>
    ) => {
      const { type, props = {}, data } = action.payload;
      state.isOpen = true;
      state.modalType = type;
      state.modalProps = props;
      state.modalData = data;
    },
    closeModal: (state) => {
      state.isOpen = false;
      state.modalType = null;
      state.modalProps = {};
      state.modalData = undefined;
    },
    updateModalProps: (
      state,
      action: PayloadAction<Record<string, unknown>>
    ) => {
      state.modalProps = { ...state.modalProps, ...action.payload };
    },
    setModalData: (state, action: PayloadAction<any>) => {
      state.modalData = action.payload;
    },
    setModalType: (state, action: PayloadAction<string | null>) => {
      state.modalType = action.payload;
    },
  },
});

// Export actions
export const {
  openModal,
  closeModal,
  updateModalProps,
  setModalData,
  setModalType,
} = modalSlice.actions;

// Export reducer
export default modalSlice.reducer;
