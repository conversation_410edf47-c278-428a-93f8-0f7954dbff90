"use client";

import type React from "react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Icon } from "@iconify/react";
import { useModal } from "@/store/compatibility";

interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
}

interface Reference {
  id: string;
  name: string;
  position: string;
  contact: string;
}

interface ApplicationData {
  personalInfo: {
    fullName: FormDataEntryValue | null;
    phone: FormDataEntryValue | null;
    email: FormDataEntryValue | null;
    address: FormDataEntryValue | null;
  };
  experiences: Experience[];
  references: Reference[];
  documents: {
    cv: File | null;
    coverLetter: File | null;
  };
  jobTitle: string;
  companyName: string;
}

interface JobApplyFormProps {
  jobTitle?: string;
  companyName?: string;
  productId?: string;
  submitAction?: string; // Action type instead of function
  onCancel?: () => void;
}

export default function JobApplyForm({
  jobTitle = "Position",
  companyName = "Company",
  productId,
  submitAction,
  onCancel,
}: JobApplyFormProps) {
  const { closeModal } = useModal();
  const [experiences, setExperiences] = useState<Experience[]>([]);
  const [references, setReferences] = useState<Reference[]>([]);
  const [cvFile, setCvFile] = useState<File | null>(null);
  const [coverLetterFile, setCoverLetterFile] = useState<File | null>(null);

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      company: "",
      position: "",
      duration: "",
    };
    setExperiences([...experiences, newExperience]);
  };

  const removeExperience = (id: string) => {
    setExperiences(experiences.filter((exp) => exp.id !== id));
  };

  const updateExperience = (
    id: string,
    field: keyof Experience,
    value: string
  ) => {
    setExperiences(
      experiences.map((exp) =>
        exp.id === id ? { ...exp, [field]: value } : exp
      )
    );
  };

  const addReference = () => {
    const newReference: Reference = {
      id: Date.now().toString(),
      name: "",
      position: "",
      contact: "",
    };
    setReferences([...references, newReference]);
  };

  const removeReference = (id: string) => {
    setReferences(references.filter((ref) => ref.id !== id));
  };

  const updateReference = (
    id: string,
    field: keyof Reference,
    value: string
  ) => {
    setReferences(
      references.map((ref) =>
        ref.id === id ? { ...ref, [field]: value } : ref
      )
    );
  };

  const handleFileUpload = (file: File | null, type: "cv" | "coverLetter") => {
    if (type === "cv") {
      setCvFile(file);
    } else {
      setCoverLetterFile(file);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);

    const applicationData = {
      personalInfo: {
        fullName: formData.get("fullName"),
        phone: formData.get("phone"),
        email: formData.get("email"),
        address: formData.get("address"),
      },
      experiences,
      references,
      documents: {
        cv: cvFile,
        coverLetter: coverLetterFile,
      },
      jobTitle,
      companyName,
      productId,
    };

    // Handle action type instead of calling function directly
    if (submitAction === "SUBMIT_JOB_APPLICATION") {
      console.log("Job application submitted:", applicationData);
      // Here you would typically dispatch an action or call an API
      // For now, we'll just show a success message and close the modal
      closeModal();
      alert("Application submitted successfully!");
    }
  };

  return (
    <div className="w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
      <div className="text-center p-4 sm:p-6 flex-shrink-0">
        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900">
          Job Application
        </h2>
        <p className="text-gray-600 mt-2 text-sm sm:text-base">
          Applying for{" "}
          <span className="font-semibold text-base sm:text-lg lg:text-xl">
            {jobTitle}
          </span>{" "}
          at <span className="font-semibold">{companyName}</span>
        </p>
      </div>
      {/* underline  */}
      <div className="w-full bg-gray-500" />

      <div className="flex-1 overflow-y-auto p-4 sm:p-6">
        <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
          {/* Personal Information */}
          <div>
            <div className="flex items-center gap-2 mb-3 sm:mb-4">
              <Icon
                icon="mdi:account"
                className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600"
              />
              <h3 className="text-base sm:text-lg font-semibold">
                Personal Information
              </h3>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-2">
                <Label htmlFor="fullName" className="text-sm">
                  Full Name *
                </Label>
                <Input
                  id="fullName"
                  name="fullName"
                  placeholder="Ram Shrestha"
                  className="h-10 sm:h-11 text-sm sm:text-base"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm">
                  Phone *
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  placeholder="9841234567"
                  className="h-10 sm:h-11 text-sm sm:text-base"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm">
                  Email *
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className="h-10 sm:h-11 text-sm sm:text-base"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address" className="text-sm">
                  Address *
                </Label>
                <Input
                  id="address"
                  name="address"
                  placeholder="Madhyapur Thimi"
                  className="h-10 sm:h-11 text-sm sm:text-base"
                  required
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Experience Section */}
          <div>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-3">
              <div className="flex items-center gap-2">
                <Icon
                  icon="mdi:briefcase"
                  className="w-4 h-4 sm:w-5 sm:h-5 text-green-600"
                />
                <h3 className="text-base sm:text-lg font-semibold">
                  Experience Details
                </h3>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addExperience}
                className="gap-2 bg-transparent w-full sm:w-auto text-sm"
              >
                <Icon icon="mdi:plus" className="w-4 h-4" />
                Add Experience
              </Button>
            </div>

            {experiences.length === 0 ? (
              <div className="text-center py-6 sm:py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                <Icon
                  icon="mdi:briefcase"
                  className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 text-gray-300"
                />
                <p className="text-sm sm:text-base">No experience added yet</p>
                <p className="text-xs sm:text-sm">
                  Click &quot;Add Experience&quot; to get started
                </p>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {experiences.map((exp) => (
                  <Card key={exp.id} className="border-l-4 border-l-green-500">
                    <CardContent className="p-3 sm:p-4">
                      <div className="flex justify-between items-start mb-3">
                        <Badge
                          variant="secondary"
                          className="bg-green-100 text-green-700 text-xs sm:text-sm"
                        >
                          Experience {experiences.indexOf(exp) + 1}
                        </Badge>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeExperience(exp.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 h-8 w-8 p-0"
                        >
                          <Icon icon="mdi:close" className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm">Company Name</Label>
                          <Input
                            placeholder="Company name"
                            value={exp.company}
                            onChange={(e) =>
                              updateExperience(
                                exp.id,
                                "company",
                                e.target.value
                              )
                            }
                            className="h-9 sm:h-10 text-sm sm:text-base"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Position</Label>
                          <Input
                            placeholder="Job position"
                            value={exp.position}
                            onChange={(e) =>
                              updateExperience(
                                exp.id,
                                "position",
                                e.target.value
                              )
                            }
                            className="h-9 sm:h-10 text-sm sm:text-base"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Duration</Label>
                          <Input
                            placeholder="e.g., 2020-2022"
                            value={exp.duration}
                            onChange={(e) =>
                              updateExperience(
                                exp.id,
                                "duration",
                                e.target.value
                              )
                            }
                            className="h-9 sm:h-10 text-sm sm:text-base"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* References Section */}
          <div>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-3">
              <div className="flex items-center gap-2">
                <Icon
                  icon="mdi:account-group"
                  className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600"
                />
                <h3 className="text-base sm:text-lg font-semibold">
                  Reference Details
                </h3>
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addReference}
                className="gap-2 bg-transparent w-full sm:w-auto text-sm"
              >
                <Icon icon="mdi:plus" className="w-4 h-4" />
                Add Reference
              </Button>
            </div>

            {references.length === 0 ? (
              <div className="text-center py-6 sm:py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
                <Icon
                  icon="mdi:account-group"
                  className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 text-gray-300"
                />
                <p className="text-sm sm:text-base">No references added yet</p>
                <p className="text-xs sm:text-sm">
                  Click &quot;Add Reference&quot; to get started
                </p>
              </div>
            ) : (
              <div className="space-y-3 sm:space-y-4">
                {references.map((ref) => (
                  <Card key={ref.id} className="border-l-4 border-l-purple-500">
                    <CardContent className="p-3 sm:p-4">
                      <div className="flex justify-between items-start mb-3">
                        <Badge
                          variant="secondary"
                          className="bg-purple-100 text-purple-700 text-xs sm:text-sm"
                        >
                          Reference {references.indexOf(ref) + 1}
                        </Badge>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeReference(ref.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 h-8 w-8 p-0"
                        >
                          <Icon icon="mdi:close" className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm">Reference Name</Label>
                          <Input
                            placeholder="Full name"
                            value={ref.name}
                            onChange={(e) =>
                              updateReference(ref.id, "name", e.target.value)
                            }
                            className="h-9 sm:h-10 text-sm sm:text-base"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Position</Label>
                          <Input
                            placeholder="Job title"
                            value={ref.position}
                            onChange={(e) =>
                              updateReference(
                                ref.id,
                                "position",
                                e.target.value
                              )
                            }
                            className="h-9 sm:h-10 text-sm sm:text-base"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm">Contact</Label>
                          <Input
                            placeholder="Phone or email"
                            value={ref.contact}
                            onChange={(e) =>
                              updateReference(ref.id, "contact", e.target.value)
                            }
                            className="h-9 sm:h-10 text-sm sm:text-base"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* File Uploads */}
          <div>
            <div className="flex items-center gap-2 mb-3 sm:mb-4">
              <Icon
                icon="mdi:file-document-outline"
                className="w-4 h-4 sm:w-5 sm:h-5 text-orange-600"
              />
              <h3 className="text-base sm:text-lg font-semibold">Documents</h3>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-2">
                <Label htmlFor="cv" className="text-sm">
                  Upload CV *
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 text-center hover:border-gray-400 transition-colors">
                  <Icon
                    icon="mdi:upload"
                    className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400"
                  />
                  <p className="text-xs sm:text-sm text-gray-600 mb-2">
                    {cvFile ? cvFile.name : "Choose file or drag and drop"}
                  </p>
                  <input
                    id="cv"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    onChange={(e) =>
                      handleFileUpload(e.target.files?.[0] || null, "cv")
                    }
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => document.getElementById("cv")?.click()}
                    className="text-xs sm:text-sm"
                  >
                    Browse Files
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="coverLetter" className="text-sm">
                  Upload Cover Letter
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 sm:p-6 text-center hover:border-gray-400 transition-colors">
                  <Icon
                    icon="mdi:upload"
                    className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 text-gray-400"
                  />
                  <p className="text-xs sm:text-sm text-gray-600 mb-2">
                    {coverLetterFile
                      ? coverLetterFile.name
                      : "Choose file or drag and drop"}
                  </p>
                  <input
                    id="coverLetter"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    onChange={(e) =>
                      handleFileUpload(
                        e.target.files?.[0] || null,
                        "coverLetter"
                      )
                    }
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      document.getElementById("coverLetter")?.click()
                    }
                    className="text-xs sm:text-sm"
                  >
                    Browse Files
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row justify-center sm:justify-end gap-3 pt-4 sm:pt-6">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                size="lg"
                className="px-6 sm:px-8 w-full sm:w-auto"
                onClick={onCancel}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              size="lg"
              className="px-6 sm:px-8 bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
            >
              Submit Application
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
