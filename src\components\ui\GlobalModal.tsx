"use client";

import React from "react";
import { useAppSelector } from "@/store/hooks";
import {
  selectModalIsOpen,
  selectModalType,
  selectModalProps,
  selectModalData,
} from "@/store/selectors";
import { closeModal } from "@/store/slices/modalSlice";
import { useAppDispatch } from "@/store/hooks";
import { getModalComponent } from "@/store/modalRegistry";
import { Dialog, DialogContent } from "./dialog";

export function GlobalModal() {
  const dispatch = useAppDispatch();
  const isOpen = useAppSelector(selectModalIsOpen);
  const modalType = useAppSelector(selectModalType);
  const modalProps = useAppSelector(selectModalProps);
  const modalData = useAppSelector(selectModalData);

  const handleClose = () => {
    dispatch(closeModal());
  };

  // Get the modal component from the registry
  const ModalComponent = modalType ? getModalComponent(modalType) : null;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent showCloseButton={true} {...modalProps}>
        {ModalComponent && (
          <ModalComponent
            modalData={modalData}
            modalProps={modalProps}
            onClose={handleClose}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
