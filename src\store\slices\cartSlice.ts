import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { CartItem, Product } from "@/types/ecommerce";
import { AddToCartDto, UpdateCartItemDto, BulkRemoveDto } from "@/types/orders";
import { CartService } from "@/services/cart-service";
import { CartPersistenceService } from "@/services/cart-persistence";
import {
  getUserFriendlyMessage,
  logError,
  withRetry,
} from "@/utils/error-handling";

// Define the cart state interface
export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  totalAmount?: number; // Backend total amount
  currency: string;
  loading: boolean;
  error?: string;
  cartId?: string;
  lastSyncAt?: string;
  isOnline: boolean;
  pendingOperations: PendingCartOperation[];
  syncInProgress: boolean;
}

// Define pending operations for offline support
export interface PendingCartOperation {
  id: string;
  type: "add" | "update" | "remove" | "clear";
  data: any;
  timestamp: string;
}

// Helper function to convert backend CartItemDto to frontend CartItem
const convertCartItemDtoToCartItem = (dto: any): CartItem => {
  return {
    id: dto.id,
    product: {
      id: dto.advertisement.id,
      title: dto.advertisement.title,
      slug: dto.advertisement.slug || dto.advertisement.id,
      price: dto.unitPrice,
      currency: dto.advertisement.currency || "NPR",
      images: dto.advertisement.images?.map((img: any) => img.url) || [
        "/placeholder-image.jpg",
      ],
      category: dto.advertisement.category?.name || "Unknown",
      subcategory: dto.advertisement.subcategory?.name,
      location: dto.advertisement.location?.city || "Unknown",
      condition: dto.advertisement.condition || "used",
      featured: dto.advertisement.isFeatured || false,
      status: dto.advertisement.status || "active",
      description: dto.advertisement.description || "",
      seller: {
        id: dto.advertisement.user?.id || "",
        name: dto.advertisement.user?.username || "Unknown",
        avatar:
          dto.advertisement.user?.profilePictureUrl || "/default-avatar.png",
        rating: 4.5,
      },
      postedDate: dto.advertisement.createdAt,
      postedAt: dto.advertisement.createdAt,
      views: dto.advertisement.views || 0,
      delivery: dto.advertisement.delivery || "pickup",
    },
    quantity: dto.quantity,
    addedAt: dto.addedAt,
  };
};

// Helper function to convert frontend Product to backend AddToCartDto
const convertProductToAddToCartDto = (
  product: Product,
  quantity: number
): AddToCartDto => {
  return {
    advertisementId: product.id,
    quantity,
  };
};

// Initial state
const initialState: CartState = {
  items: [],
  totalItems: 0,
  totalPrice: 0,
  totalAmount: 0,
  currency: "NPR",
  loading: false,
  error: undefined,
  cartId: undefined,
  lastSyncAt: undefined,
  isOnline: typeof navigator !== "undefined" ? navigator.onLine : true,
  pendingOperations: [],
  syncInProgress: false,
};

// Helper function to calculate totals
const calculateTotals = (items: CartItem[]) => {
  const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = items.reduce(
    (sum, item) => sum + item.product.price * item.quantity,
    0
  );
  return { totalItems, totalPrice };
};

// Async thunks for cart operations
export const fetchCart = createAsyncThunk(
  "cart/fetchCart",
  async (_, { rejectWithValue, getState }) => {
    try {
      const cartResponse = await CartService.getCart();

      // If we have local cart data, merge it with server data
      if (CartPersistenceService.isStorageAvailable()) {
        const localCart = CartPersistenceService.loadCartState();
        if (localCart && localCart.items && localCart.items.length > 0) {
          const mergedCart = CartPersistenceService.mergeCartStates(localCart, {
            items: cartResponse.items.map(convertCartItemDtoToCartItem),
            totalItems: cartResponse.totalItems,
            totalPrice: cartResponse.totalAmount,
            totalAmount: cartResponse.totalAmount,
            currency: cartResponse.currency,
            cartId: cartResponse.id,
          });

          // Return merged data
          return {
            ...cartResponse,
            items:
              mergedCart.items?.map((item) => ({
                id: item.id,
                advertisementId: item.product.id,
                quantity: item.quantity,
                addedAt: item.addedAt,
              })) || cartResponse.items,
            totalItems: mergedCart.totalItems || cartResponse.totalItems,
            totalAmount: mergedCart.totalAmount || cartResponse.totalAmount,
          };
        }
      }

      return cartResponse;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch cart");
    }
  }
);

// New thunk for initializing cart with persistence
export const initializeCartWithPersistence = createAsyncThunk(
  "cart/initializeCartWithPersistence",
  async (_, { dispatch, rejectWithValue }) => {
    try {
      // First, try to load from localStorage
      if (CartPersistenceService.isStorageAvailable()) {
        const savedCart = CartPersistenceService.loadCartState();
        const pendingOps = CartPersistenceService.loadPendingOperations();

        if (savedCart && savedCart.items && savedCart.items.length > 0) {
          // Load saved cart items locally first
          savedCart.items.forEach((item) => {
            dispatch(
              addToCart({ product: item.product, quantity: item.quantity })
            );
          });
        }

        // Load pending operations
        if (pendingOps.length > 0) {
          pendingOps.forEach((op) => {
            dispatch(addPendingOperation(op));
          });
        }
      }

      // Then try to fetch from server and merge
      try {
        await dispatch(fetchCart()).unwrap();
      } catch (serverError) {
        // If server fetch fails, continue with local data
        console.warn(
          "Failed to fetch cart from server, using local data:",
          serverError
        );
      }

      return { success: true };
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to initialize cart");
    }
  }
);

export const addToCartAsync = createAsyncThunk(
  "cart/addToCartAsync",
  async (data: AddToCartDto, { rejectWithValue }) => {
    try {
      // Use retry mechanism for network resilience
      const cartResponse = await withRetry(
        () => CartService.addToCart(data),
        2, // Max 2 retries
        1000 // 1 second delay
      );
      return cartResponse;
    } catch (error: any) {
      // Log error for debugging
      logError(error, `Adding item to cart: ${data.advertisementId}`);

      // Return user-friendly error message
      const friendlyMessage = getUserFriendlyMessage(error);
      return rejectWithValue(friendlyMessage);
    }
  }
);

export const updateCartItemAsync = createAsyncThunk(
  "cart/updateCartItemAsync",
  async (
    { itemId, data }: { itemId: string; data: UpdateCartItemDto },
    { rejectWithValue }
  ) => {
    try {
      // Use retry mechanism for network resilience
      const cartResponse = await withRetry(
        () => CartService.updateCartItem(itemId, data),
        2, // Max 2 retries
        1000 // 1 second delay
      );
      return cartResponse;
    } catch (error: any) {
      // Log error for debugging
      logError(error, `Updating cart item: ${itemId}`);

      // Return user-friendly error message
      const friendlyMessage = getUserFriendlyMessage(error);
      return rejectWithValue(friendlyMessage);
    }
  }
);

export const removeCartItemAsync = createAsyncThunk(
  "cart/removeCartItemAsync",
  async (itemId: string, { rejectWithValue }) => {
    try {
      const cartResponse = await CartService.removeCartItem(itemId);
      return cartResponse;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to remove cart item");
    }
  }
);

export const bulkRemoveCartItemsAsync = createAsyncThunk(
  "cart/bulkRemoveCartItemsAsync",
  async (data: BulkRemoveDto, { rejectWithValue }) => {
    try {
      const cartResponse = await CartService.bulkRemoveItems(data);
      return cartResponse;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to remove cart items");
    }
  }
);

export const clearCartAsync = createAsyncThunk(
  "cart/clearCartAsync",
  async (_, { rejectWithValue }) => {
    try {
      await CartService.clearCart();
      return;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to clear cart");
    }
  }
);

export const syncCartWithServer = createAsyncThunk(
  "cart/syncCartWithServer",
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as { cart: CartState };
      const { pendingOperations } = state.cart;

      // Process pending operations first
      if (pendingOperations.length > 0) {
        for (const operation of pendingOperations) {
          try {
            switch (operation.type) {
              case "add":
                await CartService.addToCart(operation.data);
                break;
              case "update":
                await CartService.updateCartItem(
                  operation.data.itemId,
                  operation.data.updateData
                );
                break;
              case "remove":
                await CartService.removeCartItem(operation.data.itemId);
                break;
              case "clear":
                await CartService.clearCart();
                break;
            }
          } catch (opError) {
            console.error(`Failed to sync operation ${operation.id}:`, opError);
          }
        }
      }

      // Fetch latest cart state
      const cartResponse = await CartService.syncCart();
      return { cartResponse, clearedOperations: pendingOperations };
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to sync cart");
    }
  }
);

// New thunk for handling online/offline state changes
export const setOnlineStatus = createAsyncThunk(
  "cart/setOnlineStatus",
  async (isOnline: boolean, { dispatch, getState }) => {
    if (isOnline) {
      // When coming back online, sync with server
      const state = getState() as { cart: CartState };
      if (state.cart.pendingOperations.length > 0) {
        await dispatch(syncCartWithServer());
      }
    }
    return isOnline;
  }
);

// Enhanced thunk for processing pending operations
export const processPendingOperations = createAsyncThunk(
  "cart/processPendingOperations",
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { cart: CartState };
      const { pendingOperations, isOnline } = state.cart;

      if (!isOnline || pendingOperations.length === 0) {
        return { processedCount: 0 };
      }

      let processedCount = 0;
      const failedOperations: PendingCartOperation[] = [];

      for (const operation of pendingOperations) {
        try {
          switch (operation.type) {
            case "add":
              await CartService.addToCart(operation.data);
              break;
            case "update":
              await CartService.updateCartItem(
                operation.data.itemId,
                operation.data.updateData
              );
              break;
            case "remove":
              await CartService.removeCartItem(operation.data.itemId);
              break;
            case "clear":
              await CartService.clearCart();
              break;
          }
          processedCount++;
        } catch (error) {
          console.error(`Failed to process operation ${operation.id}:`, error);
          failedOperations.push(operation);
        }
      }

      return { processedCount, failedOperations };
    } catch (error: any) {
      return rejectWithValue(
        error.message || "Failed to process pending operations"
      );
    }
  }
);

// Convenience thunk for adding products to cart (converts Product to AddToCartDto)
export const addProductToCart = createAsyncThunk(
  "cart/addProductToCart",
  async (
    { product, quantity }: { product: Product; quantity: number },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const addToCartData = convertProductToAddToCartDto(product, quantity);
      return await dispatch(addToCartAsync(addToCartData)).unwrap();
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to add product to cart");
    }
  }
);

// Create the cart slice
const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    addToCart: (
      state,
      action: PayloadAction<{ product: Product; quantity: number }>
    ) => {
      const { product, quantity } = action.payload;
      const existingItemIndex = state.items.findIndex(
        (item) => item.id === product.id
      );

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        state.items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item
        const newItem: CartItem = {
          id: product.id,
          product,
          quantity,
          addedAt: new Date().toISOString(),
        };
        state.items.push(newItem);
      }

      // Recalculate totals
      const totals = calculateTotals(state.items);
      state.totalItems = totals.totalItems;
      state.totalPrice = totals.totalPrice;
      state.totalAmount = totals.totalPrice;
    },
    removeFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter((item) => item.id !== action.payload);

      // Recalculate totals
      const totals = calculateTotals(state.items);
      state.totalItems = totals.totalItems;
      state.totalPrice = totals.totalPrice;
      state.totalAmount = totals.totalPrice;
    },
    updateCartQuantity: (
      state,
      action: PayloadAction<{ productId: string; quantity: number }>
    ) => {
      const { productId, quantity } = action.payload;
      const itemIndex = state.items.findIndex((item) => item.id === productId);

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          // Remove item if quantity is 0 or less
          state.items.splice(itemIndex, 1);
        } else {
          // Update quantity
          state.items[itemIndex].quantity = quantity;
        }

        // Recalculate totals
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalPrice = totals.totalPrice;
        state.totalAmount = totals.totalPrice;
      }
    },
    clearCart: (state) => {
      state.items = [];
      state.totalItems = 0;
      state.totalPrice = 0;
      state.totalAmount = 0;
      state.error = undefined;
    },
    setCartLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setCartError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },
    clearCartError: (state) => {
      state.error = undefined;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.currency = action.payload;
    },
    // New reducers for synchronization
    addPendingOperation: (
      state,
      action: PayloadAction<PendingCartOperation>
    ) => {
      state.pendingOperations.push(action.payload);
    },
    removePendingOperation: (state, action: PayloadAction<string>) => {
      state.pendingOperations = state.pendingOperations.filter(
        (op) => op.id !== action.payload
      );
    },
    clearPendingOperations: (state) => {
      state.pendingOperations = [];
    },
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    setSyncInProgress: (state, action: PayloadAction<boolean>) => {
      state.syncInProgress = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch cart
      .addCase(fetchCart.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.loading = false;
        const cartResponse = action.payload;
        state.cartId = cartResponse.id;
        state.items = cartResponse.items.map(convertCartItemDtoToCartItem);
        state.totalItems = cartResponse.totalItems;
        state.totalPrice = cartResponse.totalAmount;
        state.totalAmount = cartResponse.totalAmount;
        state.currency = cartResponse.currency;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Initialize cart with persistence
      .addCase(initializeCartWithPersistence.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(initializeCartWithPersistence.fulfilled, (state) => {
        state.loading = false;
        // Cart items are already loaded by the thunk
      })
      .addCase(initializeCartWithPersistence.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Add to cart
      .addCase(addToCartAsync.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(addToCartAsync.fulfilled, (state, action) => {
        state.loading = false;
        const cartResponse = action.payload;
        state.cartId = cartResponse.id;
        state.items = cartResponse.items.map(convertCartItemDtoToCartItem);
        state.totalItems = cartResponse.totalItems;
        state.totalPrice = cartResponse.totalAmount;
        state.totalAmount = cartResponse.totalAmount;
        state.currency = cartResponse.currency;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(addToCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update cart item
      .addCase(updateCartItemAsync.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(updateCartItemAsync.fulfilled, (state, action) => {
        state.loading = false;
        const cartResponse = action.payload;
        state.cartId = cartResponse.id;
        state.items = cartResponse.items.map(convertCartItemDtoToCartItem);
        state.totalItems = cartResponse.totalItems;
        state.totalPrice = cartResponse.totalAmount;
        state.totalAmount = cartResponse.totalAmount;
        state.currency = cartResponse.currency;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(updateCartItemAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Remove cart item
      .addCase(removeCartItemAsync.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(removeCartItemAsync.fulfilled, (state, action) => {
        state.loading = false;
        const cartResponse = action.payload;
        state.cartId = cartResponse.id;
        state.items = cartResponse.items.map(convertCartItemDtoToCartItem);
        state.totalItems = cartResponse.totalItems;
        state.totalPrice = cartResponse.totalAmount;
        state.totalAmount = cartResponse.totalAmount;
        state.currency = cartResponse.currency;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(removeCartItemAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Bulk remove cart items
      .addCase(bulkRemoveCartItemsAsync.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(bulkRemoveCartItemsAsync.fulfilled, (state, action) => {
        state.loading = false;
        const cartResponse = action.payload;
        state.cartId = cartResponse.id;
        state.items = cartResponse.items.map(convertCartItemDtoToCartItem);
        state.totalItems = cartResponse.totalItems;
        state.totalPrice = cartResponse.totalAmount;
        state.totalAmount = cartResponse.totalAmount;
        state.currency = cartResponse.currency;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(bulkRemoveCartItemsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Clear cart
      .addCase(clearCartAsync.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(clearCartAsync.fulfilled, (state) => {
        state.loading = false;
        state.items = [];
        state.totalItems = 0;
        state.totalPrice = 0;
        state.totalAmount = 0;
        state.cartId = undefined;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(clearCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Sync cart with server
      .addCase(syncCartWithServer.pending, (state) => {
        state.loading = true;
        state.error = undefined;
      })
      .addCase(syncCartWithServer.fulfilled, (state, action) => {
        state.loading = false;
        state.syncInProgress = false;
        const { cartResponse } = action.payload;
        state.cartId = cartResponse.id;
        state.items = cartResponse.items.map(convertCartItemDtoToCartItem);
        state.totalItems = cartResponse.totalItems;
        state.totalPrice = cartResponse.totalAmount;
        state.totalAmount = cartResponse.totalAmount;
        state.currency = cartResponse.currency;
        state.lastSyncAt = new Date().toISOString();
        // Clear pending operations after successful sync
        state.pendingOperations = [];
      })
      .addCase(syncCartWithServer.rejected, (state, action) => {
        state.loading = false;
        state.syncInProgress = false;
        state.error = action.payload as string;
      })
      // Set online status
      .addCase(setOnlineStatus.fulfilled, (state, action) => {
        state.isOnline = action.payload;
      })
      // Process pending operations
      .addCase(processPendingOperations.pending, (state) => {
        state.syncInProgress = true;
      })
      .addCase(processPendingOperations.fulfilled, (state, action) => {
        state.syncInProgress = false;
        const { failedOperations } = action.payload;
        // Keep only failed operations
        state.pendingOperations = failedOperations || [];
      })
      .addCase(processPendingOperations.rejected, (state, action) => {
        state.syncInProgress = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  addToCart,
  removeFromCart,
  updateCartQuantity,
  clearCart,
  setCartLoading,
  setCartError,
  clearCartError,
  setCurrency,
  addPendingOperation,
  removePendingOperation,
  clearPendingOperations,
  setOnlineStatus: setOnlineStatusSync,
  setSyncInProgress,
} = cartSlice.actions;

// Export reducer
export default cartSlice.reducer;
