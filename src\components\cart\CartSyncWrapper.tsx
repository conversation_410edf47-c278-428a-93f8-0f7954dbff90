"use client";

import { useEffect, useState } from "react";
import { useAppSelector } from "@/store/hooks";

interface CartSyncWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * CartSyncWrapper Component
 * 
 * Ensures that cart-related hooks are only used after the Redux store
 * is properly initialized and the cart state is available.
 * This prevents undefined access errors during initial hydration.
 */
export function CartSyncWrapper({ children, fallback }: CartSyncWrapperProps) {
  const [isReady, setIsReady] = useState(false);
  const cartState = useAppSelector((state) => state?.cart);

  useEffect(() => {
    // Check if cart state is properly initialized
    if (cartState && typeof cartState.pendingOperations !== 'undefined') {
      setIsReady(true);
    }
  }, [cartState]);

  if (!isReady) {
    return fallback || null;
  }

  return <>{children}</>;
}
