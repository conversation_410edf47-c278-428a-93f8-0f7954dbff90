import type { Product } from "@/types/ecommerce";

interface SpecificationsTabProps {
  product: Product;
}

export function SpecificationsTab({ product }: SpecificationsTabProps) {
  const specifications = [
    { label: "Brand", value: product.brand || "Toyota" },
    { label: "Model", value: "Corolla" },
    { label: "Year", value: "2020" },
    { label: "Condition", value: product.condition },
    { label: "Fuel Type", value: "Petrol" },
    { label: "Transmission", value: "Automatic" },
    { label: "Engine", value: "1.8L" },
    { label: "Mileage", value: "45,000 km" },
    { label: "Color", value: "White" },
    { label: "Body Type", value: "Sedan" },
    { label: "Doors", value: "4" },
    { label: "Seats", value: "5" },
  ];

  return (
    <div className="space-y-4 my-auto">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Specifications
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 space-x-1 gap-4">
        {specifications.map((spec, index) => (
          <div
            key={index}
            className="flex justify-between items-center py-3 px-4 bg-gray-100 rounded-lg"
          >
            <span className="text-gray-600 font-medium">{spec.label}:</span>
            <span className="text-gray-900 font-semibold capitalize">
              {spec.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
