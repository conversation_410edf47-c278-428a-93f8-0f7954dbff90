"use client";

import React, { createContext, useContext, ReactNode, useEffect } from "react";
import { useAppSelector } from "./hooks";
import {
  selectEcommerceState,
  selectCurrentUser,
  selectIsAuthenticated,
  selectUserLoading,
  selectUserError,
  selectModalIsOpen,
  selectModalType,
  selectModalProps,
  selectModalData,
} from "./selectors";
// Categories are now handled by RTK Query
import { fetchProducts as fetchProductsAction } from "./slices/productSlice";
import { setCategories } from "./slices/categorySlice";
import {
  getCurrentUser,
  loginUser,
  logoutUser,
  updateUserProfile,
  clearUserError,
} from "./slices/userSlice";
import { openModal, closeModal } from "./slices/modalSlice";
import { useAppDispatch } from "./hooks";
import { useGetCategoriesQuery } from "./api/categoriesApi";
import { getAuthToken } from "@/lib/api";

// Compatibility context for existing components that use the old context API
interface CompatibilityContextType {
  state: any;
  dispatch: any;
}

const CompatibilityContext = createContext<
  CompatibilityContextType | undefined
>(undefined);

interface CompatibilityProviderProps {
  children: ReactNode;
}

export function CompatibilityProvider({
  children,
}: CompatibilityProviderProps) {
  const dispatch = useAppDispatch();
  const ecommerceState = useAppSelector(selectEcommerceState);

  // Initialize categories using RTK Query
  const {
    data: categories,
    error: categoriesError,
    isLoading: categoriesLoading,
  } = useGetCategoriesQuery();

  // Debug logging for categories API
  console.log("CompatibilityProvider - Categories API Debug:", {
    categories: categories?.length || 0,
    categoriesError,
    categoriesLoading,
    categoriesData: categories,
  });

  // Initialize data on mount (similar to original EcommerceProvider)
  useEffect(() => {
    // Categories are automatically fetched by RTK Query hook above
    dispatch(fetchProductsAction());
  }, [dispatch]);

  // Dispatch categories to Redux store when they're loaded from RTK Query
  useEffect(() => {
    if (categories && categories.length > 0) {
      console.log("Categories loaded from API:", categories.length);
      dispatch(setCategories(categories));
    } else if (categoriesError) {
      console.error("Failed to load categories from API:", categoriesError);
    }
  }, [categories, categoriesError, dispatch]);

  // Create a compatibility dispatch function that maps old actions to new ones
  const compatibilityDispatch = (action: any) => {
    // This is a simplified mapping - you might need to extend this based on your needs
    console.warn(
      "Using compatibility dispatch. Consider migrating to Redux actions:",
      action
    );

    // Map old actions to new Redux actions
    switch (action.type) {
      case "SET_CATEGORIES":
        // Handle via Redux slice
        break;
      case "SET_PRODUCTS":
        // Handle via Redux slice
        break;
      // Add more mappings as needed
      default:
        console.warn("Unmapped action type:", action.type);
    }
  };

  const contextValue = {
    state: ecommerceState,
    dispatch: compatibilityDispatch,
  };

  return (
    <CompatibilityContext.Provider value={contextValue}>
      {children}
    </CompatibilityContext.Provider>
  );
}

// Compatibility hook that mimics the original useEcommerce hook
export function useEcommerce() {
  const context = useContext(CompatibilityContext);
  if (context === undefined) {
    throw new Error("useEcommerce must be used within a CompatibilityProvider");
  }
  return context;
}

// User context compatibility
interface UserCompatibilityContextType {
  currentUser: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (credentials: any) => Promise<void>;
  logout: () => Promise<void>;
  updateProfile: (updates: any) => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
}

const UserCompatibilityContext = createContext<
  UserCompatibilityContextType | undefined
>(undefined);

export function UserCompatibilityProvider({
  children,
}: {
  children: ReactNode;
}) {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectCurrentUser);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const isLoading = useAppSelector(selectUserLoading);
  const error = useAppSelector(selectUserError);

  // Initialize user on mount - only if authenticated
  useEffect(() => {
    const initializeUser = async () => {
      // Only try to get current user if there's an auth token
      const authToken = getAuthToken();
      if (!authToken) {
        console.log("No auth token found, skipping user initialization");
        return;
      }

      try {
        await dispatch(getCurrentUser()).unwrap();
      } catch (error) {
        console.error("Failed to initialize user:", error);
        // If token is invalid, it will be cleared by the auth service
      }
    };

    initializeUser();
  }, [dispatch]);

  const login = async (credentials: any) => {
    try {
      await dispatch(loginUser(credentials)).unwrap();
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await dispatch(logoutUser()).unwrap();
    } catch (error) {
      console.error("Logout failed:", error);
      throw error;
    }
  };

  const updateProfile = async (updates: any) => {
    try {
      await dispatch(updateUserProfile(updates)).unwrap();
    } catch (error) {
      console.error("Profile update failed:", error);
      throw error;
    }
  };

  const refreshUser = async () => {
    try {
      await dispatch(getCurrentUser()).unwrap();
    } catch (error) {
      console.error("Failed to refresh user:", error);
    }
  };

  const clearError = () => {
    dispatch(clearUserError());
  };

  const contextValue = {
    currentUser,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    updateProfile,
    refreshUser,
    clearError,
  };

  return (
    <UserCompatibilityContext.Provider value={contextValue}>
      {children}
    </UserCompatibilityContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserCompatibilityContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserCompatibilityProvider");
  }
  return context;
}

// Modal context compatibility
interface ModalCompatibilityContextType {
  isOpen: boolean;
  modalType: string | null;
  modalProps: Record<string, unknown>;
  modalData?: any;
  openModal: (
    type: string,
    data?: any,
    props?: Record<string, unknown>
  ) => void;
  closeModal: () => void;
}

const ModalCompatibilityContext = createContext<
  ModalCompatibilityContextType | undefined
>(undefined);

export function ModalCompatibilityProvider({
  children,
}: {
  children: ReactNode;
}) {
  const dispatch = useAppDispatch();
  const isOpen = useAppSelector(selectModalIsOpen);
  const modalType = useAppSelector(selectModalType);
  const modalProps = useAppSelector(selectModalProps);
  const modalData = useAppSelector(selectModalData);

  const openModalAction = (
    type: string,
    data?: any,
    props: Record<string, unknown> = {}
  ) => {
    dispatch(openModal({ type, data, props }));
  };

  const closeModalAction = () => {
    dispatch(closeModal());
  };

  const contextValue = {
    isOpen,
    modalType,
    modalProps,
    modalData,
    openModal: openModalAction,
    closeModal: closeModalAction,
  };

  return (
    <ModalCompatibilityContext.Provider value={contextValue}>
      {children}
    </ModalCompatibilityContext.Provider>
  );
}

export function useModal() {
  const context = useContext(ModalCompatibilityContext);
  if (context === undefined) {
    throw new Error(
      "useModal must be used within a ModalCompatibilityProvider"
    );
  }
  return context;
}
