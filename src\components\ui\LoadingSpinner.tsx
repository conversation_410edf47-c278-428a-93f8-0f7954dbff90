import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  variant?: "default" | "primary" | "success";
  type?: "spinner" | "dots" | "pulse" | "bars";
  showText?: boolean;
  text?: string;
  className?: string;
}

export function LoadingSpinner({
  size = "md",
  variant = "default",
  type = "spinner",
  showText = false,
  text = "Loading...",
  className,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12",
  };

  const variantClasses = {
    default: "text-slate-600",
    primary: "text-blue-600",
    success: "text-green-600",
  };

  if (type === "dots") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex gap-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                "rounded-full animate-pulse",
                size === "sm"
                  ? "w-2 h-2"
                  : size === "lg"
                  ? "w-4 h-4"
                  : "w-3 h-3",
                variantClasses[variant]
              )}
              style={{
                backgroundColor: "currentColor",
                animationDelay: `${i * 0.2}s`,
                animationDuration: "1s",
              }}
            />
          ))}
        </div>
        {showText && (
          <span
            className={cn("text-sm font-medium ml-2", variantClasses[variant])}
          >
            {text}
          </span>
        )}
      </div>
    );
  }

  if (type === "pulse") {
    return (
      <div className={cn("flex items-center gap-3", className)}>
        <div
          className={cn(
            "rounded-full animate-pulse",
            sizeClasses[size],
            variantClasses[variant]
          )}
          style={{
            backgroundColor: "currentColor",
            animationDuration: "1.5s",
          }}
        />
        {showText && (
          <span className={cn("text-sm font-medium", variantClasses[variant])}>
            {text}
          </span>
        )}
      </div>
    );
  }

  if (type === "bars") {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <div className="flex items-end gap-1">
          {[0, 1, 2, 3].map((i) => (
            <div
              key={i}
              className={cn(
                "animate-pulse",
                size === "sm" ? "w-1" : size === "lg" ? "w-2" : "w-1.5",
                variantClasses[variant]
              )}
              style={{
                backgroundColor: "currentColor",
                height:
                  size === "sm" ? "12px" : size === "lg" ? "24px" : "16px",
                animationDelay: `${i * 0.15}s`,
                animationDuration: "1.2s",
              }}
            />
          ))}
        </div>
        {showText && (
          <span
            className={cn("text-sm font-medium ml-2", variantClasses[variant])}
          >
            {text}
          </span>
        )}
      </div>
    );
  }

  // Default spinner type
  return (
    <div className={cn("flex items-center gap-3", className)}>
      <div
        className={cn(
          "animate-spin rounded-full border-2 border-current border-t-transparent",
          sizeClasses[size],
          variantClasses[variant]
        )}
      />
      {showText && (
        <span className={cn("text-sm font-medium", variantClasses[variant])}>
          {text}
        </span>
      )}
    </div>
  );
}
