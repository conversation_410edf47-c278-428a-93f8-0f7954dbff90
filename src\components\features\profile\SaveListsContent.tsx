"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Icon } from "@iconify/react";
import { useEcommerceActions } from "@/store/hooks";
import { Product } from "@/types/ecommerce";
import { generateSlug } from "@/utils/slug-utils";

// Mock saved items data converted to Product format
const mockSavedItems: Product[] = [
  {
    id: "1",
    slug: generateSlug("iPhone 14 Pro Max", "1"),
    title: "iPhone 14 Pro Max",
    description: "Latest iPhone with excellent camera quality and performance.",
    price: 95000,
    currency: "Rs.",
    images: [
      "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=300&fit=crop&crop=center",
    ],
    category: "electronics",
    subcategory: "phones",
    location: "Mumbai, Maharashtra",
    seller: {
      id: "seller1",
      name: "TechStore",
      rating: 4.8,
    },
    condition: "used" as const,
    brand: "Apple",
    postedAt: "2024-01-15",
    delivery: {
      available: true,
      type: "both" as const,
    },
    featured: true,
    status: "active" as const,
    originalPrice: 129900,
  },
  {
    id: "2",
    slug: generateSlug("MacBook Air M2", "2"),
    title: "MacBook Air M2",
    description: "Powerful MacBook Air with M2 chip for professional work.",
    price: 89000,
    currency: "Rs.",
    images: [
      "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=300&fit=crop&crop=center",
    ],
    category: "electronics",
    subcategory: "laptops",
    location: "Delhi, Delhi",
    seller: {
      id: "seller2",
      name: "AppleHub",
      rating: 4.9,
    },
    condition: "used" as const,
    brand: "Apple",
    postedAt: "2024-01-12",
    delivery: {
      available: true,
      type: "both" as const,
    },
    featured: true,
    status: "active" as const,
    originalPrice: 119900,
  },
  {
    id: "3",
    slug: generateSlug("Canon EOS R5", "3"),
    title: "Canon EOS R5",
    description: "Professional camera with excellent image quality.",
    price: 245000,
    currency: "Rs.",
    images: [
      "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=300&fit=crop&crop=center",
    ],
    category: "photography",
    subcategory: "cameras",
    location: "Bangalore, Karnataka",
    seller: {
      id: "seller3",
      name: "CameraWorld",
      rating: 4.7,
    },
    condition: "used" as const,
    brand: "Canon",
    postedAt: "2024-01-10",
    delivery: {
      available: true,
      type: "both" as const,
    },
    featured: true,
    status: "active" as const,
    originalPrice: 339995,
  },
  {
    id: "4",
    slug: generateSlug("Sony WH-1000XM5", "4"),
    title: "Sony WH-1000XM5",
    description:
      "Premium noise-cancelling headphones with excellent sound quality.",
    price: 24990,
    currency: "Rs.",
    images: [
      "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=300&fit=crop&crop=center",
    ],
    category: "electronics",
    subcategory: "audio",
    location: "Chennai, Tamil Nadu",
    seller: {
      id: "seller4",
      name: "AudioTech",
      rating: 4.6,
    },
    condition: "brand new" as const,
    brand: "Sony",
    postedAt: "2024-01-08",
    delivery: {
      available: true,
      type: "both" as const,
    },
    featured: true,
    status: "active" as const,
    originalPrice: 29990,
  },
  {
    id: "5",
    slug: generateSlug("Nike Air Jordan 1", "5"),
    title: "Nike Air Jordan 1",
    description: "Classic basketball shoes in excellent condition.",
    price: 12995,
    currency: "Rs.",
    images: [
      "https://images.unsplash.com/photo-1556906781-9a412961c28c?w=400&h=300&fit=crop&crop=center",
    ],
    category: "fashion",
    subcategory: "shoes",
    location: "Pune, Maharashtra",
    seller: {
      id: "seller5",
      name: "SneakerHub",
      rating: 4.5,
    },
    condition: "brand new" as const,
    brand: "Nike",
    postedAt: "2024-01-05",
    delivery: {
      available: true,
      type: "both" as const,
    },
    featured: true,
    status: "active" as const,
    originalPrice: 16995,
  },
  {
    id: "6",
    slug: generateSlug("Gaming Chair RGB", "6"),
    title: "Gaming Chair RGB",
    description: "Comfortable gaming chair with RGB lighting.",
    price: 18500,
    currency: "Rs.",
    images: ["/assets/images/placeholders/placeholder.jpg"],
    category: "furniture",
    subcategory: "chairs",
    location: "Hyderabad, Telangana",
    seller: {
      id: "seller6",
      name: "GamerZone",
      rating: 4.4,
    },
    condition: "used" as const,
    brand: "Generic",
    postedAt: "2024-01-03",
    delivery: {
      available: true,
      type: "both" as const,
    },
    featured: true,
    status: "active" as const,
    originalPrice: 25000,
  },
];

interface SaveListsContentProps {
  showHeader?: boolean;
}

export default function SaveListsContent({
  showHeader = true,
}: SaveListsContentProps) {
  const { addToCart, addProductToCart } = useEcommerceActions();
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [addingToCart, setAddingToCart] = useState<Set<string>>(new Set());

  const filteredItems = mockSavedItems.filter((item) =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleItemSelection = (itemId: string) => {
    setSelectedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter((id) => id !== itemId)
        : [...prev, itemId]
    );
  };

  const removeSelectedItems = () => {
    // In a real app, this would make an API call
    console.log("Removing items:", selectedItems);
    setSelectedItems([]);
  };

  const handleAddToCart = async (product: Product) => {
    if (addingToCart.has(product.id)) return;

    setAddingToCart((prev) => new Set(prev).add(product.id));
    try {
      await addProductToCart(product, 1);
      // Optional: Show a success message or toast
      console.log(`Added ${product.title} to cart`);
    } catch (error) {
      console.error("Failed to add product to cart:", error);
      // Fallback to local cart action
      addToCart(product, 1);
    } finally {
      setAddingToCart((prev) => {
        const newSet = new Set(prev);
        newSet.delete(product.id);
        return newSet;
      });
    }
  };

  const calculateDiscount = (originalPrice: number, currentPrice: number) => {
    const discount = ((originalPrice - currentPrice) / originalPrice) * 100;
    return Math.round(discount);
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      {showHeader && (
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Save Lists
            </h2>
            <p className="text-gray-600">
              {filteredItems.length} items saved for later
            </p>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="p-2"
              >
                <Icon icon="lucide:grid-3x3" className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="p-2"
              >
                <Icon icon="lucide:list" className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Search and Actions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Icon
                  icon="lucide:search"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
                />
                <Input
                  placeholder="Search saved items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-gray-50 border-gray-200 rounded-full"
                />
              </div>
              <Button variant="outline" size="sm">
                <Icon icon="lucide:filter" className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>

            {selectedItems.length > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {selectedItems.length} selected
                </span>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={removeSelectedItems}
                >
                  <Icon icon="lucide:trash-2" className="w-4 h-4 mr-2" />
                  Remove
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Items Grid/List */}
      {viewMode === "grid" ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredItems.map((item) => (
            <Card
              key={item.id}
              className={`group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                selectedItems.includes(item.id)
                  ? "ring-2 ring-teal-500 shadow-lg"
                  : ""
              }`}
              onClick={() => toggleItemSelection(item.id)}
            >
              <CardContent className="p-0">
                <div className="relative">
                  <Image
                    src={item.images[0] || "/placeholder.svg"}
                    alt={item.title}
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover rounded-t-lg"
                  />
                  {item.originalPrice && (
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-red-500 text-white">
                        {calculateDiscount(item.originalPrice, item.price)}% off
                      </Badge>
                    </div>
                  )}
                  <div className="absolute top-3 right-3 flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Remove from save list
                      }}
                    >
                      <Icon
                        icon="lucide:heart"
                        className="w-4 h-4 text-red-500 fill-current"
                      />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Icon icon="lucide:more-vertical" className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">{item.condition}</p>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg font-bold text-gray-900">
                      {item.currency}
                      {item.price.toLocaleString()}
                    </span>
                    {item.originalPrice && (
                      <span className="text-sm text-gray-500 line-through">
                        {item.currency}
                        {item.originalPrice.toLocaleString()}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                    <span>{item.seller.name}</span>
                    <div className="flex items-center gap-1">
                      <Icon
                        icon="lucide:star"
                        className="w-3 h-3 text-yellow-500 fill-current"
                      />
                      <span>{item.seller.rating}</span>
                    </div>
                  </div>
                  <Button
                    className="w-full bg-teal-600 hover:bg-teal-700 disabled:opacity-50"
                    disabled={addingToCart.has(item.id)}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddToCart(item);
                    }}
                  >
                    {addingToCart.has(item.id) ? (
                      <>
                        <Icon
                          icon="lucide:loader-2"
                          className="w-4 h-4 mr-2 animate-spin"
                        />
                        Adding...
                      </>
                    ) : (
                      <>
                        <Icon
                          icon="lucide:shopping-cart"
                          className="w-4 h-4 mr-2"
                        />
                        Add to Cart
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        // List View
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <Card
              key={item.id}
              className={`group cursor-pointer transition-all duration-300 hover:shadow-md ${
                selectedItems.includes(item.id)
                  ? "ring-2 ring-teal-500 shadow-lg"
                  : ""
              }`}
              onClick={() => toggleItemSelection(item.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <Image
                    src={item.images[0] || "/placeholder.svg"}
                    alt={item.title}
                    width={120}
                    height={80}
                    className="w-24 h-16 object-cover rounded-lg flex-shrink-0"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 mb-1">
                      {item.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {item.condition}
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-gray-900">
                          {item.currency}
                          {item.price.toLocaleString()}
                        </span>
                        {item.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {item.currency}
                            {item.originalPrice.toLocaleString()}
                          </span>
                        )}
                        {item.originalPrice && (
                          <Badge className="bg-red-500 text-white text-xs">
                            {calculateDiscount(item.originalPrice, item.price)}%
                            off
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Icon
                          icon="lucide:star"
                          className="w-3 h-3 text-yellow-500 fill-current"
                        />
                        <span>{item.seller.rating}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Remove from save list
                      }}
                    >
                      <Icon
                        icon="lucide:heart"
                        className="w-4 h-4 text-red-500 fill-current"
                      />
                    </Button>
                    <Button
                      className="bg-teal-600 hover:bg-teal-700 disabled:opacity-50"
                      size="sm"
                      disabled={addingToCart.has(item.id)}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddToCart(item);
                      }}
                    >
                      {addingToCart.has(item.id) ? (
                        <>
                          <Icon
                            icon="lucide:loader-2"
                            className="w-4 h-4 mr-2 animate-spin"
                          />
                          Adding...
                        </>
                      ) : (
                        <>
                          <Icon
                            icon="lucide:shopping-cart"
                            className="w-4 h-4 mr-2"
                          />
                          Add to Cart
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {filteredItems.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <Icon
              icon="lucide:heart"
              className="w-16 h-16 text-gray-300 mx-auto mb-4"
            />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No saved items found
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery
                ? "Try adjusting your search terms"
                : "Start saving items you're interested in"}
            </p>
            <Link href="/">
              <Button className="bg-teal-600 hover:bg-teal-700">
                Browse Products
              </Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
