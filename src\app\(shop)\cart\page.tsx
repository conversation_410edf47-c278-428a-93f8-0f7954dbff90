"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import { BackButton } from "@/components/ui/back-button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import Image from "next/image";
import { useEcommerce } from "@/store/compatibility";
import { useEcommerceActions } from "@/store/hooks";
import { useToast } from "@/components/ui/toast";
import { Header } from "@/components";
import { CartRecoveryBanner } from "@/components/features/cart";
import { CartItem } from "@/types/ecommerce";

interface CartItemWithSelection {
  id: string;
  selected: boolean;
}

export default function ViewCart() {
  const router = useRouter();
  const { state } = useEcommerce();
  const {
    removeFromCart,
    updateCartQuantity,
    fetchCart,
    removeCartItem,
    updateCartItem,
    bulkRemoveCartItems,
  } = useEcommerceActions();
  const { cart } = state;
  const { toast } = useToast();

  // Track which items are selected for checkout
  const [selectedItems, setSelectedItems] = useState<CartItemWithSelection[]>(
    cart.items.map((item: CartItem) => ({ id: item.id, selected: false }))
  );

  // Fetch cart on component mount
  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  // Update selected items when cart changes
  useEffect(() => {
    setSelectedItems(
      cart.items.map((item: CartItem) => ({ id: item.id, selected: false }))
    );
  }, [cart.items]);

  const getSelectedItems = () => {
    return cart.items.filter((item: CartItem) =>
      selectedItems.find(
        (selected) => selected.id === item.id && selected.selected
      )
    );
  };

  const selectedCartItems = getSelectedItems();
  const allSelected =
    cart.items.length > 0 && selectedCartItems.length === cart.items.length;
  const subtotal = selectedCartItems.reduce(
    (sum: number, item: CartItem) => sum + item.product.price * item.quantity,
    0
  );
  const shippingFee = 500;
  const total = subtotal + shippingFee;

  const handleSelectAll = (checked: boolean) => {
    setSelectedItems(
      cart.items.map((item: CartItem) => ({ id: item.id, selected: checked }))
    );
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    setSelectedItems((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, selected: checked } : item
      )
    );
  };

  const handleQuantityChange = async (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    try {
      // Use API-integrated action for real-time updates
      await updateCartItem(id, newQuantity);

      toast({
        type: "success",
        title: "Quantity Updated",
        description: "Cart item quantity has been updated successfully.",
        duration: 2000,
      });
    } catch (error) {
      console.error("Failed to update cart item:", error);

      toast({
        type: "error",
        title: "Update Failed",
        description:
          "Failed to update quantity. Using local update as fallback.",
        duration: 4000,
      });

      // Fallback to local state update if API fails
      updateCartQuantity(id, newQuantity);
    }
  };

  const handleDeleteItem = async (id: string) => {
    try {
      // Use API-integrated action for real-time updates
      await removeCartItem(id);

      toast({
        type: "success",
        title: "Item Removed",
        description: "Item has been removed from your cart.",
        duration: 3000,
      });
    } catch (error) {
      console.error("Failed to remove cart item:", error);

      toast({
        type: "error",
        title: "Removal Failed",
        description: "Failed to remove item. Using local removal as fallback.",
        duration: 4000,
      });

      // Fallback to local state update if API fails
      removeFromCart(id);
    }
    // Also remove from selected items
    setSelectedItems((prev) => prev.filter((item) => item.id !== id));
  };

  const handleDeleteSelected = async () => {
    const itemCount = selectedCartItems.length;
    if (itemCount === 0) return;

    try {
      // Use bulk remove for better performance
      const itemIds = selectedCartItems.map((item: CartItem) => item.id);
      await bulkRemoveCartItems(itemIds);

      toast({
        type: "success",
        title: "Items Removed",
        description: `${itemCount} item${
          itemCount > 1 ? "s" : ""
        } removed from your cart.`,
        duration: 3000,
      });
    } catch (error) {
      console.error("Failed to remove selected items:", error);

      toast({
        type: "error",
        title: "Bulk Removal Failed",
        description:
          "Failed to remove selected items. Using local removal as fallback.",
        duration: 4000,
      });

      // Fallback to local state updates
      selectedCartItems.forEach((item: CartItem) => {
        removeFromCart(item.id);
      });
    }
    // Clear selected items
    setSelectedItems([]);
  };

  return (
    <div className="min-h-screen bg-[#EFEFEF]">
      <Header />
      <div className="container-responsive-sm py-4 sm:py-6">
        {/* Mobile-optimized Back Button and Header */}
        <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
          <BackButton onClick={() => router.back()} size="lg" />
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
              Shopping Cart
            </h1>
            <p className="text-sm sm:text-base text-gray-600 mt-1">
              {cart.items.length} items in your cart
            </p>
          </div>
        </div>

        {/* Loading and Error States */}
        {cart.loading && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-2 text-teal-600">
              <Icon icon="lucide:loader-2" className="h-5 w-5 animate-spin" />
              <span>Updating cart...</span>
            </div>
          </div>
        )}

        {cart.error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 text-red-700">
              <Icon icon="lucide:alert-circle" className="h-5 w-5" />
              <span>Error: {cart.error}</span>
            </div>
          </div>
        )}

        {/* Cart Recovery Banner */}
        <CartRecoveryBanner />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Cart Items Section */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Select All Header */}
            <div className="bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between flex-wrap gap-3">
                <div className="flex items-center space-x-3 sm:space-x-4">
                  <Checkbox
                    className="h-4 w-4 sm:h-5 sm:w-5 data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                    checked={allSelected}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm sm:text-base lg:text-lg font-semibold text-gray-800">
                    SELECT ALL ({cart.items.length} ITEMS)
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDeleteSelected}
                  disabled={selectedCartItems.length === 0}
                  className={`px-3 sm:px-4 py-2 rounded-lg transition-all duration-300 text-xs sm:text-sm ${
                    selectedCartItems.length > 0
                      ? "text-red-500 hover:text-red-700 hover:bg-red-50"
                      : "text-gray-400"
                  }`}
                >
                  <Icon
                    icon="lucide:trash-2"
                    className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2"
                  />
                  <span className="hidden sm:inline">DELETE SELECTED</span>
                  <span className="sm:hidden">DELETE</span>
                </Button>
              </div>
            </div>

            {/* Cart Items */}
            <div className="space-y-3 sm:space-y-4">
              {cart.items.map((item: CartItem) => {
                const isSelected =
                  selectedItems.find((selected) => selected.id === item.id)
                    ?.selected || false;
                return (
                  <div
                    key={item.id}
                    className={`bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 border transition-all duration-300 hover:shadow-md ${
                      isSelected
                        ? "border-teal-300 shadow-sm ring-1 ring-teal-200"
                        : "border-gray-200"
                    }`}
                  >
                    {/* Mobile Layout */}
                    <div className="sm:hidden">
                      <div className="flex items-start space-x-3 mb-4">
                        <Checkbox
                          className="h-4 w-4 mt-1 data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                          checked={isSelected}
                          onCheckedChange={(checked) =>
                            handleSelectItem(item.id, checked as boolean)
                          }
                        />
                        <div className="flex-1">
                          <div className="flex space-x-3">
                            {/* Product Image */}
                            <div className="w-20 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex-shrink-0 flex items-center justify-center overflow-hidden">
                              <Image
                                src={
                                  item.product.images[0] || "/placeholder.svg"
                                }
                                alt={item.product.title}
                                width={80}
                                height={64}
                                className="object-cover w-full h-full"
                              />
                            </div>
                            {/* Product Details */}
                            <div className="flex-1 min-w-0">
                              <h3 className="text-sm font-semibold text-gray-900 mb-1 line-clamp-2">
                                {item.product.title}
                              </h3>
                              <div className="flex items-center gap-2 mb-2">
                                <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full font-medium">
                                  {item.product.condition}
                                </span>
                              </div>
                              <p className="text-lg font-bold text-teal-600">
                                {item.product.currency}
                                {item.product.price.toLocaleString()}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* Mobile Quantity and Delete Controls */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 bg-gray-50 rounded-lg p-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleQuantityChange(item.id, item.quantity - 1)
                            }
                            className="h-7 w-7 p-0 border-gray-300"
                            disabled={item.quantity <= 1}
                          >
                            <Icon icon="lucide:minus" className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center text-sm font-medium">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() =>
                              handleQuantityChange(item.id, item.quantity + 1)
                            }
                            className="h-7 w-7 p-0 border-gray-300"
                          >
                            <Icon icon="lucide:plus" className="h-3 w-3" />
                          </Button>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteItem(item.id)}
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2"
                        >
                          <Icon icon="lucide:trash-2" className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden sm:flex items-center space-x-4 lg:space-x-6">
                      {/* Checkbox */}
                      <Checkbox
                        className="h-5 w-5 data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                        checked={isSelected}
                        onCheckedChange={(checked) =>
                          handleSelectItem(item.id, checked as boolean)
                        }
                      />

                      {/* Product Image */}
                      <div className="w-20 h-16 sm:w-24 sm:h-20 lg:w-28 lg:h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg lg:rounded-xl flex-shrink-0 flex items-center justify-center overflow-hidden shadow-sm">
                        <Image
                          src={item.product.images[0] || "/placeholder.svg"}
                          alt={item.product.title}
                          width={112}
                          height={96}
                          className="object-cover w-full h-full transition-transform duration-300 hover:scale-105"
                        />
                      </div>

                      {/* Product Details */}
                      <div className="flex-1 min-w-0 space-y-2">
                        <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                          {item.product.title}
                        </h3>
                        <div className="flex items-center gap-2 mb-2">
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                            {item.product.condition}
                          </span>
                          <span className="text-sm text-gray-500">•</span>
                          <span className="text-sm text-gray-600">
                            {item.product.location}
                          </span>
                        </div>
                        <p className="text-lg sm:text-xl font-bold text-teal-600">
                          {item.product.currency}
                          {item.product.price.toLocaleString()}
                        </p>
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2 sm:space-x-3 bg-gray-50 rounded-lg p-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleQuantityChange(item.id, item.quantity - 1)
                          }
                          className="h-7 w-7 sm:h-8 sm:w-8 p-0 border-gray-300 hover:border-teal-500 hover:text-teal-600"
                          disabled={item.quantity <= 1}
                        >
                          <Icon
                            icon="lucide:minus"
                            className="h-3 w-3 sm:h-4 sm:w-4"
                          />
                        </Button>
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) =>
                            handleQuantityChange(
                              item.id,
                              Number.parseInt(e.target.value) || 1
                            )
                          }
                          className="w-12 sm:w-16 h-7 sm:h-8 text-center text-sm border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          min="1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            handleQuantityChange(item.id, item.quantity + 1)
                          }
                          className="h-7 w-7 sm:h-8 sm:w-8 p-0 border-gray-300 hover:border-teal-500 hover:text-teal-600"
                        >
                          <Icon
                            icon="lucide:plus"
                            className="h-3 w-3 sm:h-4 sm:w-4"
                          />
                        </Button>
                      </div>

                      {/* Delete Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteItem(item.id)}
                        className="text-red-500 hover:text-red-700 hover:bg-red-50 p-2 sm:p-3 rounded-lg transition-all duration-300"
                      >
                        <Icon
                          icon="lucide:trash-2"
                          className="h-4 w-4 sm:h-5 sm:w-5"
                        />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Empty Cart State */}
            {cart.items.length === 0 && (
              <div className="bg-white rounded-lg sm:rounded-xl p-8 sm:p-12 text-center border border-gray-200 shadow-sm">
                <div className="w-16 h-16 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <Icon
                    icon="lucide:shopping-cart"
                    className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400"
                  />
                </div>
                <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
                  Your cart is empty
                </h3>
                <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
                  Add some items to your cart to get started
                </p>
                <Button
                  onClick={() => router.push("/")}
                  className="bg-teal-600 hover:bg-teal-700 text-sm sm:text-base px-4 sm:px-6 py-2 sm:py-3"
                >
                  Continue Shopping
                </Button>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg sm:rounded-xl p-4 sm:p-6 lg:p-8 border border-gray-200 shadow-lg sticky top-4">
              <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-4 sm:mb-6 lg:mb-8 text-center">
                Order Summary
              </h2>

              <div className="space-y-4 sm:space-y-6 mb-4 sm:mb-6 lg:mb-8">
                <div className="flex justify-between items-center text-sm sm:text-base lg:text-lg">
                  <span className="text-gray-600">
                    Subtotal ({selectedCartItems.length} items):
                  </span>
                  <span className="font-semibold text-gray-900">
                    Rs. {subtotal.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center text-sm sm:text-base lg:text-lg">
                  <span className="text-gray-600">Shipping Fee:</span>
                  <span className="font-semibold text-gray-900">
                    Rs. {shippingFee.toLocaleString()}
                  </span>
                </div>

                <div className="border-t border-gray-200 pt-4 sm:pt-6">
                  <div className="flex justify-between items-center">
                    <span className="text-base sm:text-lg lg:text-xl font-bold text-gray-900">
                      Total:
                    </span>
                    <span className="text-lg sm:text-xl lg:text-2xl font-bold text-teal-600">
                      Rs. {total.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>

              <Button
                className="w-full bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white text-sm sm:text-base lg:text-lg py-3 sm:py-4 rounded-lg sm:rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                disabled={selectedCartItems.length === 0}
                onClick={() => router.push("/checkout")}
              >
                Proceed to Checkout ({selectedCartItems.length})
              </Button>

              {selectedCartItems.length === 0 && (
                <p className="text-center text-gray-500 text-xs sm:text-sm mt-3 sm:mt-4">
                  Select items to proceed with checkout
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
