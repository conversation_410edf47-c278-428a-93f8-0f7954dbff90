"use client";

import { Icon } from "@iconify/react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import {
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Product } from "@/types/ecommerce";
import { generatePlaceholderUrl, isPlaceholderImage } from "@/utils";
import { useModal } from "@/store/compatibility";
import { useAppDispatch } from "@/store/hooks";
import { navigateTo } from "@/store/slices/navigationSlice";

interface AddToCartModalProps {
  product: Product;
  quantity: number;
  viewCartAction?: string; // Action type instead of function
}

export default function AddToCartModal({
  product,
  quantity,
  viewCartAction,
}: AddToCartModalProps) {
  const { closeModal } = useModal();
  const dispatch = useAppDispatch();
  const totalPrice = product.price * quantity;
  const currentImage =
    product.images?.[0] || generatePlaceholderUrl(60, 40, product.title);
  const isPlaceholder = isPlaceholderImage(currentImage);

  const handleViewCart = () => {
    if (viewCartAction === "NAVIGATE_TO_CART") {
      dispatch(navigateTo("/cart"));
    }
    closeModal();
  };

  return (
    <div className="p-6">
      <DialogHeader className="text-center pb-4">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <Icon icon="lucide:check" className="w-6 h-6 text-green-600" />
        </div>
        <DialogTitle className="text-xl font-semibold text-gray-900 mb-2">
          Item Added to Cart!
        </DialogTitle>
        <DialogDescription className="text-sm text-gray-600">
          Your item has been successfully added to your shopping cart.
        </DialogDescription>
      </DialogHeader>

      <div className="space-y-4">
        {/* Product Details */}
        <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
          <div className="w-16 h-12 bg-white rounded flex-shrink-0 flex items-center justify-center">
            <Image
              src={
                isPlaceholder
                  ? generatePlaceholderUrl(60, 40, product.title)
                  : currentImage
              }
              alt={product.title}
              width={60}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {product.title}
            </h3>
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-gray-500">
                Quantity: {quantity}
              </span>
              <span className="text-xs text-gray-500">
                Price per unit: {product.currency}
                {product.price.toLocaleString()}
              </span>
            </div>
          </div>
        </div>

        {/* Total Section */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center">
            <span className="text-base font-medium text-gray-900">
              Total for this item:
            </span>
            <span className="text-base font-semibold text-gray-900">
              {product.currency}
              {totalPrice.toLocaleString()}
            </span>
          </div>
        </div>

        {/* View Cart Button */}
        <Button
          onClick={handleViewCart}
          className="w-full bg-teal-600 hover:bg-teal-700 text-white py-3 mt-6"
        >
          <Icon icon="lucide:shopping-cart" className="w-4 h-4 mr-2" />
          View Cart
        </Button>
      </div>
    </div>
  );
}
