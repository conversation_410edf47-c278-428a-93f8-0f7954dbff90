import type { Product } from "@/types/ecommerce";
import { Icon } from "@iconify/react";

interface LocationsTabProps {
  product: Product;
}

export function LocationsTab({ product }: LocationsTabProps) {
  return (
    <div className="space-y-6 my-auto">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Location Details
        </h3>

        <div className="flex items-center gap-2 mb-4">
          <Icon icon="lucide:map-pin" className="h-5 w-5 text-blue-500" />
          <span className="text-gray-900 font-medium">{product.location}</span>
        </div>
      </div>

      {/* Map Placeholder */}
      <div className="aspect-[3/1] bg-gray-100 rounded-lg overflow-hidden relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <Icon
              icon="lucide:map-pin"
              className="h-12 w-12 text-gray-400 mx-auto mb-2"
            />
            <p className="text-gray-500">
              Interactive map would be displayed here
            </p>
            <p className="text-sm text-gray-400 mt-1">
              Showing approximate location for privacy
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
