"use client";

import { CartState, PendingCartOperation } from "@/store/slices/cartSlice";
import { CartItem } from "@/types/ecommerce";

/**
 * Cart Persistence Service
 * Handles saving and loading cart data from localStorage
 */
export class CartPersistenceService {
  private static readonly CART_STORAGE_KEY = "ecommerce_cart";
  private static readonly PENDING_OPERATIONS_KEY = "ecommerce_cart_pending";
  private static readonly LAST_SYNC_KEY = "ecommerce_cart_last_sync";

  /**
   * Save cart state to localStorage
   */
  static saveCartState(cartState: Partial<CartState>): void {
    try {
      const dataToSave = {
        items: cartState.items || [],
        totalItems: cartState.totalItems || 0,
        totalPrice: cartState.totalPrice || 0,
        totalAmount: cartState.totalAmount || 0,
        currency: cartState.currency || "NPR",
        cartId: cartState.cartId,
        lastSyncAt: cartState.lastSyncAt,
        timestamp: new Date().toISOString(),
      };

      localStorage.setItem(this.CART_STORAGE_KEY, JSON.stringify(dataToSave));
    } catch (error) {
      console.error("Failed to save cart state to localStorage:", error);
    }
  }

  /**
   * Load cart state from localStorage
   */
  static loadCartState(): Partial<CartState> | null {
    try {
      const savedData = localStorage.getItem(this.CART_STORAGE_KEY);
      if (!savedData) return null;

      const parsedData = JSON.parse(savedData);
      
      // Check if data is not too old (7 days)
      const timestamp = new Date(parsedData.timestamp);
      const now = new Date();
      const sevenDays = 7 * 24 * 60 * 60 * 1000;
      
      if (now.getTime() - timestamp.getTime() > sevenDays) {
        this.clearCartState();
        return null;
      }

      return {
        items: parsedData.items || [],
        totalItems: parsedData.totalItems || 0,
        totalPrice: parsedData.totalPrice || 0,
        totalAmount: parsedData.totalAmount || 0,
        currency: parsedData.currency || "NPR",
        cartId: parsedData.cartId,
        lastSyncAt: parsedData.lastSyncAt,
      };
    } catch (error) {
      console.error("Failed to load cart state from localStorage:", error);
      return null;
    }
  }

  /**
   * Save pending operations to localStorage
   */
  static savePendingOperations(operations: PendingCartOperation[]): void {
    try {
      localStorage.setItem(this.PENDING_OPERATIONS_KEY, JSON.stringify(operations));
    } catch (error) {
      console.error("Failed to save pending operations:", error);
    }
  }

  /**
   * Load pending operations from localStorage
   */
  static loadPendingOperations(): PendingCartOperation[] {
    try {
      const savedData = localStorage.getItem(this.PENDING_OPERATIONS_KEY);
      if (!savedData) return [];

      const operations = JSON.parse(savedData);
      
      // Filter out operations older than 24 hours
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      return operations.filter((op: PendingCartOperation) => {
        const opTime = new Date(op.timestamp);
        return opTime > oneDayAgo;
      });
    } catch (error) {
      console.error("Failed to load pending operations:", error);
      return [];
    }
  }

  /**
   * Add a pending operation
   */
  static addPendingOperation(operation: PendingCartOperation): void {
    const existing = this.loadPendingOperations();
    existing.push(operation);
    this.savePendingOperations(existing);
  }

  /**
   * Remove a pending operation
   */
  static removePendingOperation(operationId: string): void {
    const existing = this.loadPendingOperations();
    const filtered = existing.filter(op => op.id !== operationId);
    this.savePendingOperations(filtered);
  }

  /**
   * Clear all pending operations
   */
  static clearPendingOperations(): void {
    try {
      localStorage.removeItem(this.PENDING_OPERATIONS_KEY);
    } catch (error) {
      console.error("Failed to clear pending operations:", error);
    }
  }

  /**
   * Save last sync timestamp
   */
  static saveLastSyncTime(timestamp: string): void {
    try {
      localStorage.setItem(this.LAST_SYNC_KEY, timestamp);
    } catch (error) {
      console.error("Failed to save last sync time:", error);
    }
  }

  /**
   * Get last sync timestamp
   */
  static getLastSyncTime(): string | null {
    try {
      return localStorage.getItem(this.LAST_SYNC_KEY);
    } catch (error) {
      console.error("Failed to get last sync time:", error);
      return null;
    }
  }

  /**
   * Clear all cart data from localStorage
   */
  static clearCartState(): void {
    try {
      localStorage.removeItem(this.CART_STORAGE_KEY);
      localStorage.removeItem(this.PENDING_OPERATIONS_KEY);
      localStorage.removeItem(this.LAST_SYNC_KEY);
    } catch (error) {
      console.error("Failed to clear cart state:", error);
    }
  }

  /**
   * Check if localStorage is available
   */
  static isStorageAvailable(): boolean {
    try {
      const test = "__storage_test__";
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Merge local cart with server cart
   * Prioritizes server data but preserves local items not on server
   */
  static mergeCartStates(
    localCart: Partial<CartState>,
    serverCart: Partial<CartState>
  ): Partial<CartState> {
    const localItems = localCart.items || [];
    const serverItems = serverCart.items || [];

    // Create a map of server items by product ID
    const serverItemsMap = new Map<string, CartItem>();
    serverItems.forEach(item => {
      serverItemsMap.set(item.product.id, item);
    });

    // Start with server items
    const mergedItems: CartItem[] = [...serverItems];

    // Add local items that don't exist on server
    localItems.forEach(localItem => {
      if (!serverItemsMap.has(localItem.product.id)) {
        mergedItems.push(localItem);
      }
    });

    // Calculate totals
    const totalItems = mergedItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalPrice = mergedItems.reduce(
      (sum, item) => sum + (item.product.price * item.quantity), 
      0
    );

    return {
      ...serverCart,
      items: mergedItems,
      totalItems,
      totalPrice,
      totalAmount: serverCart.totalAmount || totalPrice,
    };
  }

  /**
   * Create a recovery snapshot of current cart state
   */
  static createRecoverySnapshot(cartState: CartState): void {
    try {
      const snapshot = {
        ...cartState,
        snapshotTime: new Date().toISOString(),
      };
      
      localStorage.setItem(
        `${this.CART_STORAGE_KEY}_recovery`, 
        JSON.stringify(snapshot)
      );
    } catch (error) {
      console.error("Failed to create recovery snapshot:", error);
    }
  }

  /**
   * Load recovery snapshot
   */
  static loadRecoverySnapshot(): CartState | null {
    try {
      const snapshot = localStorage.getItem(`${this.CART_STORAGE_KEY}_recovery`);
      if (!snapshot) return null;

      const parsedSnapshot = JSON.parse(snapshot);
      
      // Check if snapshot is not too old (1 hour)
      const snapshotTime = new Date(parsedSnapshot.snapshotTime);
      const now = new Date();
      const oneHour = 60 * 60 * 1000;
      
      if (now.getTime() - snapshotTime.getTime() > oneHour) {
        this.clearRecoverySnapshot();
        return null;
      }

      return parsedSnapshot;
    } catch (error) {
      console.error("Failed to load recovery snapshot:", error);
      return null;
    }
  }

  /**
   * Clear recovery snapshot
   */
  static clearRecoverySnapshot(): void {
    try {
      localStorage.removeItem(`${this.CART_STORAGE_KEY}_recovery`);
    } catch (error) {
      console.error("Failed to clear recovery snapshot:", error);
    }
  }
}
