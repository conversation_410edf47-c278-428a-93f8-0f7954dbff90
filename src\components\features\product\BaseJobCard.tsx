"use client";

import { memo } from "react";
import { Icon } from "@iconify/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useModal } from "@/store/compatibility";
import { JobApplyForm } from "@/components/dynamic";
import { cn } from "@/lib/utils";
import type { JobCardProps } from "./types";
import {
  transformProductToCardData,
  generateJobData,
  formatPrice,
} from "./utils";

const BaseJobCard = memo(function BaseJobCard({
  product,
  config: _config,
  className = "",
  onViewDetails,
  onApply,
}: JobCardProps) {
  const { openModal } = useModal();
  const cardData = transformProductToCardData(product);
  const jobData = generateJobData(product);

  // Transform product data to job-specific format
  const jobTitle = product.title;
  const company = product.seller.name;
  const location = product.location;
  const salary =
    product.price > 0
      ? formatPrice(product.price, product.currency)
      : "Negotiable";
  const description = product.description;

  const handleApplyNow = () => {
    openModal(
      "JOB_APPLY_FORM",
      {
        jobTitle,
        companyName: company,
        productId: product.id,
        submitAction: "SUBMIT_JOB_APPLICATION", // Use action type instead of function
      },
      {
        className: "max-w-6xl w-[95vw] max-h-[95vh]",
      }
    );
  };

  const handleViewDetails = () => {
    onViewDetails?.(product);
  };

  return (
    <Card
      className={cn(
        "w-full border-gray-200 shadow-sm hover:shadow-lg transition-all duration-200 hover:-translate-y-1",
        className
      )}
    >
      <CardHeader className="pb-3 sm:pb-4 p-4 sm:p-6">
        <div className="flex items-start justify-between gap-3">
          <div className="flex items-start gap-3 sm:gap-4 flex-1 min-w-0">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <Icon
                icon="lucide:building-2"
                className="w-5 h-5 sm:w-6 sm:h-6 text-white"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="text-base sm:text-lg md:text-xl font-semibold text-gray-900 mb-1 line-clamp-2 leading-tight">
                {jobTitle}
              </h2>
              <p className="text-sm sm:text-base text-gray-600 mb-2 line-clamp-1">
                {company}
              </p>
              <div className="flex flex-wrap items-center gap-2 sm:gap-3 text-xs sm:text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <Icon
                    icon="lucide:map-pin"
                    className="w-3 h-3 sm:w-4 sm:h-4"
                  />
                  <span>{location}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Icon
                    icon="lucide:banknote"
                    className="w-3 h-3 sm:w-4 sm:h-4"
                  />
                  <span>{salary}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Icon icon="lucide:users" className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>{jobData.applicantCount} applicants</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-1 sm:gap-2 flex-shrink-0">
            <Badge
              variant="secondary"
              className="text-xs px-2 py-1 bg-green-100 text-green-800 border-green-200"
            >
              {jobData.jobType}
            </Badge>
            <Badge
              variant="outline"
              className="text-xs px-2 py-1 hidden sm:inline-flex"
            >
              {jobData.workMode}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-4 sm:p-6 pt-0">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4 sm:mb-6">
          <div className="flex items-center gap-2">
            <Icon
              icon="mdi:briefcase"
              className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0"
            />
            <div>
              <p className="text-xs sm:text-sm text-gray-500">
                Experience Level:
              </p>
              <p className="font-medium text-sm sm:text-base">
                {jobData.experienceLevel}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Icon
              icon="mdi:clock-outline"
              className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0"
            />
            <div>
              <p className="text-xs sm:text-sm text-gray-500">Posted:</p>
              <p className="font-medium text-sm sm:text-base">
                {cardData.timeAgo}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 sm:col-span-2 lg:col-span-1">
            <Icon
              icon="mdi:calendar"
              className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 flex-shrink-0"
            />
            <div>
              <p className="text-xs sm:text-sm text-gray-500">
                Application Deadline:
              </p>
              <p className="font-medium text-sm sm:text-base">
                {
                  new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                    .toISOString()
                    .split("T")[0]
                }
              </p>
            </div>
          </div>
        </div>

        <Separator />

        <div className="space-y-2 sm:space-y-3">
          <p className="text-gray-700 leading-relaxed line-clamp-3 text-sm sm:text-base">
            {description}
          </p>

          <div>
            <h4 className="font-medium text-gray-900 mb-2 text-sm sm:text-base">
              Key Requirements:
            </h4>
            <div className="flex flex-wrap gap-1.5 sm:gap-2">
              {jobData.requirements.map((skill, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="text-xs sm:text-sm"
                >
                  {skill}
                </Badge>
              ))}
              <Badge variant="outline" className="text-xs sm:text-sm">
                3+ years exp
              </Badge>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 mt-4 sm:mt-6">
          <Button
            onClick={handleViewDetails}
            variant="outline"
            className="flex-1 text-teal-600 border-teal-600 hover:bg-teal-50"
          >
            <Icon icon="lucide:eye" className="w-4 h-4 mr-2" />
            View Details
          </Button>
          <Button
            onClick={handleApplyNow}
            className="flex-1 bg-teal-600 hover:bg-teal-700 text-white"
          >
            <Icon icon="lucide:send" className="w-4 h-4 mr-2" />
            Apply Now
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});

export default BaseJobCard;
