"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON>er } from "@/components";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Icon } from "@iconify/react";
import { useEcommerceActions } from "@/store/hooks";
import { Product } from "@/types/ecommerce";
import { generateSlug } from "@/utils";
import { advertisementApi } from "@/store/api/advertisementApi";
import type {
  AdvertisementResponse,
  UserSummaryDto,
} from "@/types/advertisement";

// Transform advertisement response to Product format
const transformAdvertisementToProduct = (
  ad: AdvertisementResponse
): Product => {
  // Get user display name
  const getUserDisplayName = (user: UserSummaryDto): string => {
    if (user.firstName && user.lastName) {
      return `${user.firstName} ${user.lastName}`;
    }
    if (user.firstName) return user.firstName;
    if (user.lastName) return user.lastName;
    return user.username;
  };

  return {
    id: ad.id,
    slug: generateSlug(ad.title, ad.id),
    title: ad.title,
    description: ad.description,
    price: ad.price || 0,
    currency: "Rs.",
    images: ad.images?.map((img) => img.imageUrl) || [],
    category: ad.category?.name?.toLowerCase() || "general",
    subcategory: ad.subcategory?.name?.toLowerCase() || "",
    location: ad.location || ad.city || ad.state || "",
    seller: {
      id: ad.user?.id || "",
      name: getUserDisplayName(ad.user),
      rating: 4.5, // Default rating as it's not in the API response
    },
    condition: (ad.condition?.toLowerCase() as "new" | "used") || "used",
    brand: "", // Brand is not available in the API response
    postedAt: ad.createdAt,
    delivery: {
      available: true, // Default to true as delivery info is not in the API response
      type: "both" as const,
    },
    featured: ad.isFeatured || false,
    status: mapApiStatusToProductStatus(ad.status),
    originalPrice: undefined, // Original price is not available in the API response
  };
};

// Map API status to Product status
const mapApiStatusToProductStatus = (
  apiStatus: string
): "active" | "sold" | "inactive" | "hold" | "expired" => {
  switch (apiStatus.toLowerCase()) {
    case "active":
      return "active";
    case "sold":
      return "sold";
    case "expired":
      return "expired";
    case "draft":
    case "pending_approval":
    case "rejected":
    case "suspended":
      return "inactive";
    default:
      return "inactive";
  }
};

export default function SaveListsPage() {
  const { addToCart, addProductToCart } = useEcommerceActions();
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [addingToCart, setAddingToCart] = useState<Set<string>>(new Set());

  // Fetch favorite advertisements using RTK Query
  const {
    data: favoritesData,
    error,
    isLoading,
    refetch,
  } = advertisementApi.useGetFavoriteAdvertisementsQuery({
    page,
    limit,
    sortBy: "createdAt",
    sortOrder: "DESC",
  });

  // Transform API data to Product format
  const favoriteProducts: Product[] =
    favoritesData?.data?.map(transformAdvertisementToProduct) || [];

  // Filter items based on search query
  const filteredItems = favoriteProducts.filter((item) =>
    item.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Mutations for managing favorites
  const [removeFromFavorites] =
    advertisementApi.useRemoveFromFavoritesMutation();

  const toggleItemSelection = (itemId: string) => {
    setSelectedItems((prev) =>
      prev.includes(itemId)
        ? prev.filter((id) => id !== itemId)
        : [...prev, itemId]
    );
  };

  const removeSelectedItems = async () => {
    try {
      // Remove each selected item from favorites
      await Promise.all(
        selectedItems.map((itemId) => removeFromFavorites(itemId).unwrap())
      );
      setSelectedItems([]);
      // Refetch the favorites list to update the UI
      refetch();
    } catch (error) {
      console.error("Error removing items from favorites:", error);
      // You might want to show a toast notification here
    }
  };

  const removeSingleItem = async (itemId: string) => {
    try {
      await removeFromFavorites(itemId).unwrap();
      // Refetch the favorites list to update the UI
      refetch();
    } catch (error) {
      console.error("Error removing item from favorites:", error);
      // You might want to show a toast notification here
    }
  };

  const handleAddToCart = async (product: Product) => {
    if (addingToCart.has(product.id)) return;

    setAddingToCart((prev) => new Set(prev).add(product.id));
    try {
      await addProductToCart(product, 1);
      // Optional: Show a success message or toast
      console.log(`Added ${product.title} to cart`);
    } catch (error) {
      console.error("Failed to add product to cart:", error);
      // Fallback to local cart action
      addToCart(product, 1);
    } finally {
      setAddingToCart((prev) => {
        const newSet = new Set(prev);
        newSet.delete(product.id);
        return newSet;
      });
    }
  };

  const calculateDiscount = (originalPrice: number, currentPrice: number) => {
    const discount = ((originalPrice - currentPrice) / originalPrice) * 100;
    return Math.round(discount);
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <Header />
        <div className="mx-[5%] py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Icon
                icon="lucide:loader-2"
                className="w-8 h-8 text-teal-600 animate-spin mx-auto mb-4"
              />
              <p className="text-gray-600">Loading your saved items...</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
        <Header />
        <div className="mx-[5%] py-8">
          <div className="flex items-center justify-center py-12">
            <Card className="text-center p-8">
              <CardContent>
                <Icon
                  icon="lucide:alert-circle"
                  className="w-16 h-16 text-red-500 mx-auto mb-4"
                />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  Error Loading Saved Items
                </h3>
                <p className="text-gray-600 mb-4">
                  There was an error loading your saved items. Please try again.
                </p>
                <Button
                  onClick={() => refetch()}
                  className="bg-teal-600 hover:bg-teal-700"
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: "#EFEFEF" }}>
      <Header />

      <div className="mx-[5%] py-8">
        {/* Header Section */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link href="/profile">
              <Button variant="ghost" size="sm" className="p-2">
                <Icon icon="lucide:arrow-left" className="w-5 h-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Save Lists
              </h1>
              <p className="text-gray-600">
                {filteredItems.length} items saved for later
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="p-2"
              >
                <Icon icon="lucide:grid-3x3" className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="p-2"
              >
                <Icon icon="lucide:list" className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Search and Actions */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-4 flex-1">
                <div className="relative flex-1 max-w-md">
                  <Icon
                    icon="lucide:search"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
                  />
                  <Input
                    placeholder="Search saved items..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-gray-50 border-gray-200 rounded-full"
                  />
                </div>
                <Button variant="outline" size="sm">
                  <Icon icon="lucide:filter" className="w-4 h-4 mr-2" />
                  Filter
                </Button>
              </div>

              {selectedItems.length > 0 && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">
                    {selectedItems.length} selected
                  </span>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={removeSelectedItems}
                  >
                    <Icon icon="lucide:trash-2" className="w-4 h-4 mr-2" />
                    Remove
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Items Grid/List */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map((item) => (
              <Card
                key={item.id}
                className={`group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                  selectedItems.includes(item.id)
                    ? "ring-2 ring-teal-500 shadow-lg"
                    : ""
                }`}
                onClick={() => toggleItemSelection(item.id)}
              >
                <CardContent className="p-0">
                  <div className="relative">
                    <Image
                      src={item.images[0] || "/placeholder.svg"}
                      alt={item.title}
                      width={300}
                      height={200}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                    {item.originalPrice && (
                      <div className="absolute top-3 left-3">
                        <Badge className="bg-red-500 text-white">
                          {calculateDiscount(item.originalPrice, item.price)}%
                          off
                        </Badge>
                      </div>
                    )}
                    <div className="absolute top-3 right-3 flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeSingleItem(item.id);
                        }}
                      >
                        <Icon
                          icon="lucide:heart"
                          className="w-4 h-4 text-red-500 fill-current"
                        />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Icon icon="lucide:more-vertical" className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {item.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {item.condition}
                    </p>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg font-bold text-gray-900">
                        {item.currency}
                        {item.price.toLocaleString()}
                      </span>
                      {item.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {item.currency}
                          {item.originalPrice.toLocaleString()}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                      <span>{item.seller.name}</span>
                      <div className="flex items-center gap-1">
                        <Icon
                          icon="lucide:star"
                          className="w-3 h-3 text-yellow-500 fill-current"
                        />
                        <span>{item.seller.rating}</span>
                      </div>
                    </div>
                    <Button
                      className="w-full bg-teal-600 hover:bg-teal-700 disabled:opacity-50"
                      disabled={addingToCart.has(item.id)}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddToCart(item);
                      }}
                    >
                      {addingToCart.has(item.id) ? (
                        <>
                          <Icon
                            icon="lucide:loader-2"
                            className="w-4 h-4 mr-2 animate-spin"
                          />
                          Adding...
                        </>
                      ) : (
                        <>
                          <Icon
                            icon="lucide:shopping-cart"
                            className="w-4 h-4 mr-2"
                          />
                          Add to Cart
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          // List View
          <div className="space-y-4">
            {filteredItems.map((item) => (
              <Card
                key={item.id}
                className={`group cursor-pointer transition-all duration-300 hover:shadow-md ${
                  selectedItems.includes(item.id)
                    ? "ring-2 ring-teal-500 shadow-lg"
                    : ""
                }`}
                onClick={() => toggleItemSelection(item.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    <Image
                      src={item.images[0] || "/placeholder.svg"}
                      alt={item.title}
                      width={120}
                      height={80}
                      className="w-24 h-16 object-cover rounded-lg flex-shrink-0"
                    />
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {item.title}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {item.condition}
                      </p>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-gray-900">
                            {item.currency}
                            {item.price.toLocaleString()}
                          </span>
                          {item.originalPrice && (
                            <span className="text-sm text-gray-500 line-through">
                              {item.currency}
                              {item.originalPrice.toLocaleString()}
                            </span>
                          )}
                          {item.originalPrice && (
                            <Badge className="bg-red-500 text-white text-xs">
                              {calculateDiscount(
                                item.originalPrice,
                                item.price
                              )}
                              % off
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <Icon
                            icon="lucide:star"
                            className="w-3 h-3 text-yellow-500 fill-current"
                          />
                          <span>{item.seller.rating}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeSingleItem(item.id);
                        }}
                      >
                        <Icon
                          icon="lucide:heart"
                          className="w-4 h-4 text-red-500 fill-current"
                        />
                      </Button>
                      <Button
                        className="bg-teal-600 hover:bg-teal-700 disabled:opacity-50"
                        size="sm"
                        disabled={addingToCart.has(item.id)}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddToCart(item);
                        }}
                      >
                        {addingToCart.has(item.id) ? (
                          <>
                            <Icon
                              icon="lucide:loader-2"
                              className="w-4 h-4 mr-2 animate-spin"
                            />
                            Adding...
                          </>
                        ) : (
                          <>
                            <Icon
                              icon="lucide:shopping-cart"
                              className="w-4 h-4 mr-2"
                            />
                            Add to Cart
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {filteredItems.length === 0 && !isLoading && (
          <Card className="text-center py-12">
            <CardContent>
              <Icon
                icon="lucide:heart"
                className="w-16 h-16 text-gray-300 mx-auto mb-4"
              />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No saved items found
              </h3>
              <p className="text-gray-600 mb-4">
                {searchQuery
                  ? "Try adjusting your search terms"
                  : "Start saving items you're interested in"}
              </p>
              <Link href="/">
                <Button className="bg-teal-600 hover:bg-teal-700">
                  Browse Products
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {favoritesData?.meta && favoritesData.meta.totalPages > 1 && (
          <div className="flex items-center justify-center gap-2 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={!favoritesData.meta.hasPreviousPage}
            >
              <Icon icon="lucide:chevron-left" className="w-4 h-4" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              {Array.from(
                { length: Math.min(5, favoritesData.meta.totalPages) },
                (_, i) => {
                  const pageNum =
                    Math.max(
                      1,
                      Math.min(
                        favoritesData.meta.totalPages - 4,
                        Math.max(1, page - 2)
                      )
                    ) + i;

                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(pageNum)}
                      className="w-10 h-10 p-0"
                    >
                      {pageNum}
                    </Button>
                  );
                }
              )}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={!favoritesData.meta.hasNextPage}
            >
              Next
              <Icon icon="lucide:chevron-right" className="w-4 h-4" />
            </Button>
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
}
