import { createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "../index";
import { Product, ActiveFilters, SortBy } from "@/types/ecommerce";
import { applyFiltersAndSort } from "@/utils/product-filters";

// Complex thunk for filtering and sorting products
export const filterAndSortProducts = createAsyncThunk<
  Product[],
  void,
  { state: RootState }
>("ecommerce/filterAndSortProducts", async (_, { getState }) => {
  const state = getState();
  const { products, sortBy } = state.product;
  const { selectedCategory } = state.category;
  const { activeFilters } = state.filter;
  const { query: searchQuery, location: searchLocation } = state.search;

  // Apply filters and sorting
  const filteredProducts = applyFiltersAndSort(
    products,
    selectedCategory,
    searchQuery,
    searchLocation,
    activeFilters,
    sortBy
  );

  return filteredProducts;
});

// Thunk for initializing the application
export const initializeApp = createAsyncThunk<void, void, { state: RootState }>(
  "ecommerce/initializeApp",
  async (_, { dispatch }) => {
    // Categories are now handled by RTK Query - no need to initialize here

    // Initialize products
    await dispatch({ type: "product/fetchProducts" });

    // Initialize user if token exists
    try {
      await dispatch({ type: "user/getCurrentUser" });

      // If user is authenticated, initialize cart with persistence
      await dispatch({ type: "cart/initializeCartWithPersistence" });
    } catch (error) {
      // User not authenticated, still try to initialize cart from local storage
      console.log(
        "User not authenticated, initializing cart from local storage"
      );
      try {
        await dispatch({ type: "cart/initializeCartWithPersistence" });
      } catch (cartError) {
        console.warn("Failed to initialize cart:", cartError);
      }
    }
  }
);

// Thunk for category selection with filter updates
export const selectCategoryWithFilters = createAsyncThunk<
  void,
  string | null,
  { state: RootState }
>("ecommerce/selectCategoryWithFilters", async (categoryId, { dispatch }) => {
  // Select category
  dispatch({ type: "category/selectCategory", payload: categoryId });

  // Update available filters for the category
  await dispatch({
    type: "filter/updateAvailableFilters",
    payload: categoryId,
  });

  // Clear active filters when changing category
  dispatch({ type: "filter/clearFilters" });

  // Re-filter products
  await dispatch(filterAndSortProducts());
});

// Thunk for updating filters and re-filtering products
export const updateFiltersAndProducts = createAsyncThunk<
  void,
  ActiveFilters,
  { state: RootState }
>("ecommerce/updateFiltersAndProducts", async (filters, { dispatch }) => {
  // Update active filters
  dispatch({ type: "filter/setActiveFilters", payload: filters });

  // Re-filter products
  await dispatch(filterAndSortProducts());
});

// Thunk for updating sort and re-filtering products
export const updateSortAndProducts = createAsyncThunk<
  void,
  SortBy,
  { state: RootState }
>("ecommerce/updateSortAndProducts", async (sortBy, { dispatch }) => {
  // Update sort
  dispatch({ type: "product/setSortBy", payload: sortBy });

  // Re-filter products
  await dispatch(filterAndSortProducts());
});

// Thunk for search with filtering
export const performSearchWithFilters = createAsyncThunk<
  void,
  { query: string; category?: string; location?: string },
  { state: RootState }
>("ecommerce/performSearchWithFilters", async (searchParams, { dispatch }) => {
  // Update search parameters
  dispatch({ type: "search/setSearchQuery", payload: searchParams.query });
  if (searchParams.category) {
    dispatch({
      type: "search/setSearchCategory",
      payload: searchParams.category,
    });
  }
  if (searchParams.location) {
    dispatch({
      type: "search/setSearchLocation",
      payload: searchParams.location,
    });
  }

  // Perform search (if you have an API endpoint)
  // await dispatch({ type: 'search/performSearch', payload: searchParams });

  // Re-filter products with new search criteria
  await dispatch(filterAndSortProducts());
});

// Thunk for adding to cart with API integration
export const addToCartOptimistic = createAsyncThunk<
  void,
  { product: Product; quantity: number },
  { state: RootState }
>(
  "ecommerce/addToCartOptimistic",
  async ({ product, quantity }, { dispatch, rejectWithValue }) => {
    try {
      // Use the new API-integrated cart thunk
      await dispatch({
        type: "cart/addProductToCart",
        payload: { product, quantity },
      });
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to add item to cart");
    }
  }
);

// Thunk for bulk operations
export const performBulkCartOperations = createAsyncThunk<
  void,
  { operation: "clear" | "sync" | "fetch" },
  { state: RootState }
>(
  "ecommerce/performBulkCartOperations",
  async ({ operation }, { dispatch }) => {
    switch (operation) {
      case "clear":
        await dispatch({ type: "cart/clearCartAsync" });
        break;

      case "sync":
        await dispatch({ type: "cart/syncCartWithServer" });
        break;

      case "fetch":
        await dispatch({ type: "cart/fetchCart" });
        break;

      default:
        throw new Error(`Unknown bulk operation: ${operation}`);
    }
  }
);

// Thunk for user profile operations
export const updateUserProfileWithValidation = createAsyncThunk<
  void,
  Partial<any>, // Replace with proper UserProfile type
  { state: RootState }
>(
  "ecommerce/updateUserProfileWithValidation",
  async (updates, { dispatch, getState, rejectWithValue }) => {
    const state = getState();

    if (!state.user.isAuthenticated) {
      return rejectWithValue("User must be authenticated to update profile");
    }

    try {
      // Validate updates (add your validation logic here)
      if (updates.email && !isValidEmail(updates.email)) {
        return rejectWithValue("Invalid email format");
      }

      // Update profile
      await dispatch({ type: "user/updateUserProfile", payload: updates });
    } catch (error) {
      return rejectWithValue("Failed to update profile");
    }
  }
);

// Helper function for email validation
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
