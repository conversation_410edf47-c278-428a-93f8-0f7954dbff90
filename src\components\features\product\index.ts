// New unified components
export { default as BaseProductCard } from "./BaseProductCard";
export { default as BaseJobCard } from "./BaseJobCard";
export { default as ProductDisplay } from "./ProductDisplay";
export { default as BaseProductDetailView } from "./BaseProductDetailView";

// Updated components using new base components
export { default as ProductGrid } from "./ProductGrid";
export { default as VerticalProductList } from "./VerticalProductList";

// Legacy components (kept for backward compatibility)
export { ProductCard } from "./ProductCard";
export { default as ProductCardDetailed } from "./ProductCardDetailed";
export { default as JobCard } from "./JobCard";

// Shared types and utilities
export * from "./types";
export * from "./utils";

// Re-export from detail subdirectory
export * from "./detail";

// Re-export from review subdirectory
export * from "./review";
