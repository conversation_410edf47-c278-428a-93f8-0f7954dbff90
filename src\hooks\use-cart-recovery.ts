"use client";

import { useEffect, useCallback, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { CartPersistenceService } from "@/services/cart-persistence";
import {
  addToCart,
  clearCart,
  setCurrency,
  addPendingOperation,
  clearPendingOperations,
  PendingCartOperation,
  CartState,
} from "@/store/slices/cartSlice";
import { useToast } from "@/components/ui/toast";

/**
 * Hook for cart recovery and persistence functionality
 */
export function useCartRecovery() {
  const dispatch = useAppDispatch();
  const { toast } = useToast();
  const cartState = useAppSelector((state) => state.cart);
  const [hasRecoveryData, setHasRecoveryData] = useState(false);
  const [isRecovering, setIsRecovering] = useState(false);

  // Check for recovery data on mount
  useEffect(() => {
    if (CartPersistenceService.isStorageAvailable()) {
      const recoverySnapshot = CartPersistenceService.loadRecoverySnapshot();
      const savedCart = CartPersistenceService.loadCartState();
      const pendingOps = CartPersistenceService.loadPendingOperations();

      setHasRecoveryData(
        !!(recoverySnapshot || savedCart || pendingOps.length > 0)
      );
    }
  }, []);

  // Auto-save cart state to localStorage
  useEffect(() => {
    if (
      CartPersistenceService.isStorageAvailable() &&
      cartState.items.length > 0
    ) {
      CartPersistenceService.saveCartState(cartState);
    }
  }, [cartState.items, cartState.totalItems, cartState.totalPrice]);

  // Auto-save pending operations
  useEffect(() => {
    if (CartPersistenceService.isStorageAvailable()) {
      CartPersistenceService.savePendingOperations(cartState.pendingOperations);
    }
  }, [cartState.pendingOperations]);

  // Create recovery snapshot before critical operations
  const createRecoverySnapshot = useCallback(() => {
    if (CartPersistenceService.isStorageAvailable()) {
      CartPersistenceService.createRecoverySnapshot(cartState);
    }
  }, [cartState]);

  // Recover cart from localStorage
  const recoverCart = useCallback(async () => {
    if (!CartPersistenceService.isStorageAvailable()) {
      toast({
        type: "error",
        title: "Recovery Failed",
        description: "Local storage is not available.",
        duration: 5000,
      });
      return false;
    }

    setIsRecovering(true);
    try {
      // Try recovery snapshot first
      let recoveredData: CartState | Partial<CartState> | null =
        CartPersistenceService.loadRecoverySnapshot();

      // If no recovery snapshot, try regular saved cart
      if (!recoveredData) {
        recoveredData = CartPersistenceService.loadCartState();
      }

      if (
        recoveredData &&
        recoveredData.items &&
        recoveredData.items.length > 0
      ) {
        // Clear current cart
        dispatch(clearCart());

        // Restore items
        recoveredData.items.forEach((item) => {
          dispatch(
            addToCart({ product: item.product, quantity: item.quantity })
          );
        });

        // Restore currency if available
        if (recoveredData.currency) {
          dispatch(setCurrency(recoveredData.currency));
        }

        toast({
          type: "success",
          title: "Cart Recovered",
          description: `${recoveredData.items.length} items have been restored to your cart.`,
          duration: 5000,
        });

        return true;
      }

      // Try to recover pending operations
      const pendingOps = CartPersistenceService.loadPendingOperations();
      if (pendingOps.length > 0) {
        // Add pending operations to state
        pendingOps.forEach((op) => {
          dispatch(addPendingOperation(op));
        });

        toast({
          type: "info",
          title: "Pending Operations Recovered",
          description: `${pendingOps.length} pending cart operations will be processed when you're online.`,
          duration: 5000,
        });

        return true;
      }

      toast({
        type: "info",
        title: "No Recovery Data",
        description: "No cart data found to recover.",
        duration: 3000,
      });

      return false;
    } catch (error) {
      console.error("Failed to recover cart:", error);
      toast({
        type: "error",
        title: "Recovery Failed",
        description: "Failed to recover cart data. Please try again.",
        duration: 5000,
      });
      return false;
    } finally {
      setIsRecovering(false);
    }
  }, [dispatch, toast]);

  // Clear all recovery data
  const clearRecoveryData = useCallback(() => {
    if (CartPersistenceService.isStorageAvailable()) {
      CartPersistenceService.clearCartState();
      CartPersistenceService.clearRecoverySnapshot();
      dispatch(clearPendingOperations());
      setHasRecoveryData(false);

      toast({
        type: "success",
        title: "Recovery Data Cleared",
        description: "All saved cart data has been cleared.",
        duration: 3000,
      });
    }
  }, [dispatch, toast]);

  // Merge local cart with server cart
  const mergeWithServerCart = useCallback(
    (serverCartState: any) => {
      if (!CartPersistenceService.isStorageAvailable()) {
        return serverCartState;
      }

      const localCart = CartPersistenceService.loadCartState();
      if (!localCart || !localCart.items || localCart.items.length === 0) {
        return serverCartState;
      }

      const mergedCart = CartPersistenceService.mergeCartStates(
        localCart,
        serverCartState
      );

      if (
        mergedCart.items &&
        mergedCart.items.length > localCart.items.length
      ) {
        toast({
          type: "info",
          title: "Cart Merged",
          description: "Your local cart has been merged with server data.",
          duration: 4000,
        });
      }

      return mergedCart;
    },
    [toast]
  );

  // Add a pending operation for offline scenarios
  const addPendingCartOperation = useCallback(
    (type: PendingCartOperation["type"], data: any) => {
      const operation: PendingCartOperation = {
        id: `${type}_${Date.now()}_${Math.random()
          .toString(36)
          .substring(2, 11)}`,
        type,
        data,
        timestamp: new Date().toISOString(),
      };

      dispatch(addPendingOperation(operation));

      if (CartPersistenceService.isStorageAvailable()) {
        CartPersistenceService.addPendingOperation(operation);
      }

      return operation.id;
    },
    [dispatch]
  );

  // Get recovery statistics
  const getRecoveryStats = useCallback(() => {
    if (!CartPersistenceService.isStorageAvailable()) {
      return { hasData: false, itemCount: 0, pendingOpsCount: 0 };
    }

    const savedCart = CartPersistenceService.loadCartState();
    const pendingOps = CartPersistenceService.loadPendingOperations();
    const recoverySnapshot = CartPersistenceService.loadRecoverySnapshot();

    return {
      hasData: !!(savedCart || recoverySnapshot || pendingOps.length > 0),
      itemCount:
        savedCart?.items?.length || recoverySnapshot?.items?.length || 0,
      pendingOpsCount: pendingOps.length,
      lastSyncTime: CartPersistenceService.getLastSyncTime(),
    };
  }, []);

  return {
    hasRecoveryData,
    isRecovering,
    recoverCart,
    clearRecoveryData,
    createRecoverySnapshot,
    mergeWithServerCart,
    addPendingCartOperation,
    getRecoveryStats,
    isStorageAvailable: CartPersistenceService.isStorageAvailable(),
  };
}
