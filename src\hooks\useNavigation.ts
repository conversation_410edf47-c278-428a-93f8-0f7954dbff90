"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { clearPendingNavigation } from "@/store/slices/navigationSlice";

export function useNavigation() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const pendingNavigation = useAppSelector(
    (state) => state.navigation.pendingNavigation
  );

  useEffect(() => {
    if (pendingNavigation) {
      router.push(pendingNavigation);
      dispatch(clearPendingNavigation());
    }
  }, [pendingNavigation, router, dispatch]);

  return null;
}
