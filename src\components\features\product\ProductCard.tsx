"use client";

import { Icon } from "@iconify/react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

interface ProductCardProps {
  id?: string;
  title?: string;
  price?: number;
  location?: string;
  rating?: number;
  views?: number;
  seller?: string;
  sellerId?: string; // New prop for seller ID
  timeAgo?: string;
  image?: string;
  currency?: string;
  onViewDetails?: () => void;
  onContactSeller?: () => void;
  onMore?: () => void;
  onFavorite?: () => void;
  onAddToCart?: () => void;
  product?: any; // Full product object for cart integration
}

export function ProductCard({
  title = "Toyota Corolla",
  price = 12345678,
  location = "Lalitpur",
  rating = 4.2,
  views = 300,
  seller = "John",
  sellerId,
  timeAgo = "3days ago",
  image = "/placeholder.svg?height=80&width=120",
  currency = "Rs.",
  onFavorite,
  onAddToCart,
  product,
}: ProductCardProps) {
  const handleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFavorite?.();
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAddToCart?.();
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
      <div className="flex">
        {/* Product Image - Smaller area */}
        <div className="w-20 h-20 sm:w-24 sm:h-24 relative flex-shrink-0 m-3">
          <Image
            src={image || "/placeholder.svg"}
            alt={title}
            fill
            className="object-cover rounded-lg"
          />
        </div>

        {/* Content Section - More space for details */}
        <div className="flex-1 p-3 pr-4 relative">
          {/* Heart Icon */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-1 right-1 h-6 w-6 text-gray-400 hover:text-red-500"
            onClick={handleFavorite}
          >
            <Icon icon="lucide:heart" className="h-3 w-3" />
          </Button>

          {/* Main Content */}
          <div className="space-y-1.5 pr-8">
            <h3 className="text-sm font-medium text-gray-900 line-clamp-2 leading-tight">
              {title}
            </h3>

            <div className="text-lg font-bold text-gray-900">
              {currency}
              {price.toLocaleString()}
            </div>

            <div className="flex items-center text-xs text-gray-600">
              <Icon icon="lucide:map-pin" className="h-3 w-3 mr-1" />
              {location}
            </div>

            <div className="flex items-center gap-3 text-xs text-gray-600">
              <div className="flex items-center">
                <Icon
                  icon="lucide:star"
                  className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400"
                />
                {rating}
              </div>
              <div className="flex items-center">
                <Icon icon="lucide:eye" className="h-3 w-3 mr-1" />
                {views} views
              </div>
            </div>
            {/* horizontal seperator  */}
            <div className="border-t border-gray-200 mt-2"></div>
            {/* Attribution */}
            <div className="text-xs text-gray-500 pt-1 mb-2">
              {sellerId ? (
                <Link
                  href={`/profile/${sellerId}`}
                  className="text-gray-500 hover:text-gray-700 hover:underline"
                  onClick={(e) => e.stopPropagation()}
                >
                  {seller}
                </Link>
              ) : (
                <span>{seller}</span>
              )}
              . {timeAgo}
            </div>

            {/* Add to Cart Button */}
            {product && onAddToCart && (
              <Button
                size="sm"
                variant="outline"
                onClick={handleAddToCart}
                className="w-full text-xs py-1 h-7 text-teal-600 border-teal-600 hover:bg-teal-50"
              >
                <Icon icon="lucide:shopping-cart" className="h-3 w-3 mr-1" />
                Add to Cart
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
